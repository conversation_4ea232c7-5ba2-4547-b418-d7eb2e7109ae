import 'dart:io';
import 'dart:ui';
import 'package:uuid/uuid.dart';

class CropModel {
  final double x;
  final double y;
  final double width;
  final double height;
  final bool enabled;
  final double sourceWidth;
  final double sourceHeight;

  CropModel({
    this.x = 0.0,
    this.y = 0.0,
    this.width = 1.0,
    this.height = 1.0,
    this.enabled = false,
    this.sourceWidth = 1920.0,
    this.sourceHeight = 1080.0,
  });

  /// Create crop model from normalized values (0.0 to 1.0)
  factory CropModel.fromNormalized({
    required double normalizedX,
    required double normalizedY,
    required double normalizedWidth,
    required double normalizedHeight,
    required double sourceWidth,
    required double sourceHeight,
    bool enabled = true,
  }) {
    return CropModel(
      x: normalizedX * sourceWidth,
      y: normalizedY * sourceHeight,
      width: normalizedWidth * sourceWidth,
      height: normalizedHeight * sourceHeight,
      sourceWidth: sourceWidth,
      sourceHeight: sourceHeight,
      enabled: enabled,
    );
  }

  /// Validate crop parameters
  bool get isValid {
    return x >= 0 &&
        y >= 0 &&
        width > 0 &&
        height > 0 &&
        x + width <= sourceWidth &&
        y + height <= sourceHeight;
  }

  /// Get normalized crop values (0.0 to 1.0)
  Map<String, double> get normalized {
    return {
      'x': x / sourceWidth,
      'y': y / sourceHeight,
      'width': width / sourceWidth,
      'height': height / sourceHeight,
    };
  }

  /// Generate FFmpeg crop filter with validation
  String toFFmpegFilter() {
    if (!enabled || !isValid) {
      return '';
    }

    final cropX = x.clamp(0.0, sourceWidth - 1).toInt();
    final cropY = y.clamp(0.0, sourceHeight - 1).toInt();
    final cropWidth = width.clamp(1.0, sourceWidth - cropX).toInt();
    final cropHeight = height.clamp(1.0, sourceHeight - cropY).toInt();

    return 'crop=$cropWidth:$cropHeight:$cropX:$cropY';
  }

  /// Convert to Flutter Rect
  Rect toRect() {
    return Rect.fromLTWH(x, y, width, height);
  }

  /// Get aspect ratio of the crop
  double get aspectRatio {
    return width / height;
  }

  /// Create a copy with modified values
  CropModel copyWith({
    double? x,
    double? y,
    double? width,
    double? height,
    bool? enabled,
    double? sourceWidth,
    double? sourceHeight,
  }) {
    return CropModel(
      x: x ?? this.x,
      y: y ?? this.y,
      width: width ?? this.width,
      height: height ?? this.height,
      enabled: enabled ?? this.enabled,
      sourceWidth: sourceWidth ?? this.sourceWidth,
      sourceHeight: sourceHeight ?? this.sourceHeight,
    );
  }

  /// Constrain crop to maintain aspect ratio
  CropModel constrainToAspectRatio(double targetAspectRatio) {
    final currentAspectRatio = aspectRatio;

    if ((currentAspectRatio - targetAspectRatio).abs() < 0.01) {
      return this; // Already matches
    }

    double newWidth = width;
    double newHeight = height;

    if (currentAspectRatio > targetAspectRatio) {
      // Too wide, reduce width
      newWidth = height * targetAspectRatio;
    } else {
      // Too tall, reduce height
      newHeight = width / targetAspectRatio;
    }

    // Center the crop
    final newX = x + (width - newWidth) / 2;
    final newY = y + (height - newHeight) / 2;

    return copyWith(
      x: newX.clamp(0.0, sourceWidth - newWidth),
      y: newY.clamp(0.0, sourceHeight - newHeight),
      width: newWidth,
      height: newHeight,
    );
  }

  @override
  String toString() {
    return 'CropModel(x: $x, y: $y, width: $width, height: $height, enabled: $enabled)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CropModel &&
        other.x == x &&
        other.y == y &&
        other.width == width &&
        other.height == height &&
        other.enabled == enabled &&
        other.sourceWidth == sourceWidth &&
        other.sourceHeight == sourceHeight;
  }

  @override
  int get hashCode {
    return x.hashCode ^
        y.hashCode ^
        width.hashCode ^
        height.hashCode ^
        enabled.hashCode ^
        sourceWidth.hashCode ^
        sourceHeight.hashCode;
  }
}

class TransformData {
  final double x;
  final double y;
  final double scale;
  final double rotation;

  TransformData({
    this.x = 0.0,
    this.y = 0.0,
    this.scale = 1.0,
    this.rotation = 0.0,
  });
}

class VideoTrackModel {
  final String id;
  final File originalFile;
  final File processedFile;
  final int startTime;
  final int endTime;
  final int totalDuration;
  final bool hasOriginalAudio;
  final double videoTrimStart;
  final double videoTrimEnd;
  final double originalDuration;
  final DateTime lastModified;
  final bool isImageBased;
  final double? customDuration;

  // Individual clip editing properties
  final CropModel? cropModel;
  final int rotation;
  final String? filter;
  final double playbackSpeed;
  final double volume;
  final TransformData? transform;

  VideoTrackModel({
    String? id,
    required this.originalFile,
    required this.processedFile,
    required this.startTime,
    required this.endTime,
    required this.totalDuration,
    this.hasOriginalAudio = true,
    this.videoTrimStart = 0.0,
    this.videoTrimEnd = 0.0,
    this.originalDuration = 0.0,
    DateTime? lastModified,
    this.isImageBased = false,
    this.customDuration,
    this.cropModel,
    this.rotation = 0,
    this.filter,
    this.playbackSpeed = 1.0,
    this.volume = 1.0,
    this.transform,
  })  : id = id ?? const Uuid().v4(),
        lastModified = lastModified ?? DateTime.now();

  VideoTrackModel copyWith({
    File? originalFile,
    File? processedFile,
    int? startTime,
    int? endTime,
    int? totalDuration,
    bool? hasOriginalAudio,
    double? videoTrimStart,
    double? videoTrimEnd,
    double? originalDuration,
    DateTime? lastModified,
    bool? isImageBased,
    double? customDuration,
    CropModel? cropModel,
    int? rotation,
    String? filter,
    double? playbackSpeed,
    double? volume,
    TransformData? transform,
  }) {
    return VideoTrackModel(
      id: id,
      originalFile: originalFile ?? this.originalFile,
      processedFile: processedFile ?? this.processedFile,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      totalDuration: totalDuration ?? this.totalDuration,
      hasOriginalAudio: hasOriginalAudio ?? this.hasOriginalAudio,
      videoTrimStart: videoTrimStart ?? this.videoTrimStart,
      videoTrimEnd: videoTrimEnd ?? this.videoTrimEnd,
      originalDuration: originalDuration ?? this.originalDuration,
      lastModified: lastModified ?? DateTime.now(),
      isImageBased: isImageBased ?? this.isImageBased,
      customDuration: customDuration ?? this.customDuration,
      cropModel: cropModel ?? this.cropModel,
      rotation: rotation ?? this.rotation,
      filter: filter ?? this.filter,
      playbackSpeed: playbackSpeed ?? this.playbackSpeed,
      volume: volume ?? this.volume,
      transform: transform ?? this.transform,
    );
  }
}
