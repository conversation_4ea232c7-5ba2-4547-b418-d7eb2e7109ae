import 'dart:io';
import 'dart:ui';
import 'package:uuid/uuid.dart';
import 'audio_track_model.dart';
import 'text_track_model.dart';

class CropModel {
  final double x;
  final double y;
  final double width;
  final double height;
  final bool enabled;

  CropModel({
    this.x = 0.0,
    this.y = 0.0,
    this.width = 1.0,
    this.height = 1.0,
    this.enabled = false,
  });

  String toFFmpegFilter() {
    return 'crop=${width.toInt()}:${height.toInt()}:${x.toInt()}:${y.toInt()}';
  }
}

class TransformData {
  final double x;
  final double y;
  final double scale;
  final double rotation;

  TransformData({
    this.x = 0.0,
    this.y = 0.0,
    this.scale = 1.0,
    this.rotation = 0.0,
  });
}

class VideoTrackModel {
  final String id;
  final File originalFile;
  final File processedFile;
  final int startTime;
  final int endTime;
  final int totalDuration;
  final bool hasOriginalAudio;
  final double videoTrimStart;
  final double videoTrimEnd;
  final double originalDuration;
  final DateTime lastModified;
  final bool isImageBased;
  final double? customDuration;

  // Individual clip editing properties
  final CropModel? cropModel;
  final int rotation;
  final String? filter;
  final double playbackSpeed;
  final double volume;
  final TransformData? transform;

  VideoTrackModel({
    String? id,
    required this.originalFile,
    required this.processedFile,
    required this.startTime,
    required this.endTime,
    required this.totalDuration,
    this.hasOriginalAudio = true,
    this.videoTrimStart = 0.0,
    this.videoTrimEnd = 0.0,
    this.originalDuration = 0.0,
    DateTime? lastModified,
    this.isImageBased = false,
    this.customDuration,
    this.cropModel,
    this.rotation = 0,
    this.filter,
    this.playbackSpeed = 1.0,
    this.volume = 1.0,
    this.transform,
  })  : id = id ?? const Uuid().v4(),
        lastModified = lastModified ?? DateTime.now();

  VideoTrackModel copyWith({
    File? originalFile,
    File? processedFile,
    int? startTime,
    int? endTime,
    int? totalDuration,
    bool? hasOriginalAudio,
    double? videoTrimStart,
    double? videoTrimEnd,
    double? originalDuration,
    DateTime? lastModified,
    bool? isImageBased,
    double? customDuration,
    CropModel? cropModel,
    int? rotation,
    String? filter,
    double? playbackSpeed,
    double? volume,
    TransformData? transform,
  }) {
    return VideoTrackModel(
      id: id,
      originalFile: originalFile ?? this.originalFile,
      processedFile: processedFile ?? this.processedFile,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      totalDuration: totalDuration ?? this.totalDuration,
      hasOriginalAudio: hasOriginalAudio ?? this.hasOriginalAudio,
      videoTrimStart: videoTrimStart ?? this.videoTrimStart,
      videoTrimEnd: videoTrimEnd ?? this.videoTrimEnd,
      originalDuration: originalDuration ?? this.originalDuration,
      lastModified: lastModified ?? DateTime.now(),
      isImageBased: isImageBased ?? this.isImageBased,
      customDuration: customDuration ?? this.customDuration,
      cropModel: cropModel ?? this.cropModel,
      rotation: rotation ?? this.rotation,
      filter: filter ?? this.filter,
      playbackSpeed: playbackSpeed ?? this.playbackSpeed,
      volume: volume ?? this.volume,
      transform: transform ?? this.transform,
    );
  }
}

static String buildFilterComplex({
  required List<VideoTrackModel> videoTracks,
  required List<AudioTrackModel> audioTracks,
  required List<TextTrackModel> textTracks,
  required Size outputSize,
}) {
  final filters = <String>[];
  final videoInputs = <String>[];

  // Process each video track with its individual settings
  for (int i = 0; i < videoTracks.length; i++) {
    final track = videoTracks[i];
    final inputLabel = '[$i:v]';
    final outputLabel = '[v$i]';
    
    var filterChain = inputLabel;
    
    // Apply individual clip transformations
    if (track.cropModel?.enabled == true) {
      filterChain += track.cropModel!.toFFmpegFilter() + ',';
    }
    
    if (track.rotation != 0) {
      final rotateFilter = _getRotationFilter(track.rotation);
      filterChain += '$rotateFilter,';
    }
    
    if (track.filter != null && track.filter != 'None') {
      filterChain += '${track.filter},';
    }
    
    if (track.playbackSpeed != 1.0) {
      filterChain += 'setpts=${1.0 / track.playbackSpeed}*PTS,';
    }
    
    // Scale and pad to output size (always last)
    filterChain += 'scale=${outputSize.width.toInt()}:${outputSize.height.toInt()}:'
                  'force_original_aspect_ratio=decrease,'
                  'pad=${outputSize.width.toInt()}:${outputSize.height.toInt()}:'
                  '(ow-iw)/2:(oh-ih)/2:color=black,setsar=1$outputLabel';
    
    filters.add(filterChain);
    videoInputs.add(outputLabel);
  }

  // Concatenate video tracks if multiple
  if (videoInputs.length > 1) {
    final concatFilter = videoInputs.join('') + 
                        'concat=n=${videoInputs.length}:v=1:a=0[vout]';
    filters.add(concatFilter);
  } else if (videoInputs.isNotEmpty) {
    // Rename single video output
    filters.add('${videoInputs.first}copy[vout]');
  }

  return filters.join(';');
}

static String _getRotationFilter(int rotation) {
  switch (rotation % 360) {
    case 90:
      return 'transpose=1';
    case 180:
      return 'transpose=1,transpose=1';
    case 270:
      return 'transpose=2';
    default:
      return '';
  }
}
