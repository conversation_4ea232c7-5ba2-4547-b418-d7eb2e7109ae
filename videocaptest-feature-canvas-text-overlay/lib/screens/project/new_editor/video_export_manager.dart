import 'dart:io';
import 'dart:math' as math;
import 'dart:ui' as ui;
import 'package:ai_video_creator_editor/screens/project/models/text_track_model.dart';
import 'package:ai_video_creator_editor/screens/project/models/video_track_model.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/text_overlay_manager.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/transition_picker.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/video_editor_provider.dart';
import 'package:ai_video_creator_editor/utils/text_auto_wrap_helper.dart';
import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter_new/return_code.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';

/// ✅ UPDATED: Now rotation-aware for consistent text overlay positioning
/// The VideoExportManager now properly handles video rotation when calculating
/// gaps and mapping coordinates from preview to video space, ensuring that
/// text overlays appear in the correct positions in exported videos.
class VideoExportManager {
  static Future<String> exportVideo(BuildContext context,
      {required String inputPath,
      required String outputPath,
      required double startTime,
      required double endTime,
      String? audioPath,
      double audioTrimStart = 0.0,
      double audioTrimEnd = 0.0,
      List<TextOverlay> textOverlays = const [],
      String filter = 'none',
      TransitionType transition = TransitionType.none,
      double speed = 1.0,
      double videoVolume = 1.0,
      double audioVolume = 1.0,
      int? rotation,
      Offset? minCrop,
      Offset? maxCrop,
      double? videoWidth,
      double? videoHeight,
      double? previewHeight,
      required List<VideoTrackModel> videoTracks,
      required List<TextTrackModel> textTracks,
      VideoEditorProvider? editorProvider}) async {
    final tempDir = await getTemporaryDirectory();
    String currentPath = inputPath;

    try {
      // Step 1: Apply transition
      if (transition != TransitionType.none) {
        final transitionPath =
            '${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}_transitioned_video.mp4';
        final value =
            await _applyTransitions(transitionPath, transition, videoTracks);
        if (value) currentPath = transitionPath;
      }

      // Step 2: Apply filter
      if (filter != 'none') {
        final filteredPath =
            '${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}_filtered_video.mp4';
        final value =
            await FilterManager.applyFilter(currentPath, filteredPath, filter);
        if (value) currentPath = filteredPath;
      }

      // Step 3: Apply crop FIRST (before text overlays)
      double? croppedVideoWidth = videoWidth;
      double? croppedVideoHeight = videoHeight;
      if (minCrop != null &&
          maxCrop != null &&
          videoWidth != null &&
          videoHeight != null) {
        final croppedPath =
            '${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}_cropped_video.mp4';
        final value = await _cropVideo(currentPath, croppedPath, minCrop,
            maxCrop, videoWidth, videoHeight);
        if (value) {
          currentPath = croppedPath;
          // Update dimensions to cropped video size
          croppedVideoWidth = (maxCrop.dx - minCrop.dx) * videoWidth;
          croppedVideoHeight = (maxCrop.dy - minCrop.dy) * videoHeight;
        }
      }

      if (rotation != null) {
        final rotatedPath =
            '${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}_rotated_video.mp4';
        final value = await _rotateVideo(currentPath, rotatedPath, rotation);
        if (value) currentPath = rotatedPath;
      }
      // Step 4: Apply text overlays AFTER crop (with transformed coordinates)
      if (textTracks.isNotEmpty && croppedVideoWidth != null) {
        final textOverlayPath =
            '${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}_text_overlay_video.mp4';
        final textOverlaySuccess = await _addTextOverlay(context, currentPath,
            textOverlayPath, textTracks, croppedVideoWidth,
            originalVideoWidth: videoWidth,
            originalVideoHeight: videoHeight,
            previewHeight: previewHeight,
            minCrop: minCrop,
            maxCrop: maxCrop,
            rotation: rotation); // ✅ ADDED: Pass rotation parameter

        if (textOverlaySuccess) {
          // Verify the text overlay file exists before continuing
          final textOverlayFile = File(textOverlayPath);
          if (await textOverlayFile.exists()) {
            currentPath = textOverlayPath;
            _exportLogs
                .add('Text overlay applied successfully: $textOverlayPath');
          } else {
            _exportLogs.add(
                'Text overlay processing failed: output file does not exist: $textOverlayPath');
            throw Exception(
                'Text overlay processing failed: output file does not exist');
          }
        } else {
          _exportLogs.add('Text overlay processing failed');
          throw Exception('Text overlay processing failed');
        }
      }

      // Step 5: Apply rotation
      // if (rotation != null) {
      //   final rotatedPath =
      //       '${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}_rotated_video.mp4';
      //   final value = await _rotateVideo(currentPath, rotatedPath, rotation);
      //   if (value) currentPath = rotatedPath;
      // }

      // Step 6: Merge multiple audio tracks if editor provider is available
      if (editorProvider != null) {
        final mergedAudioPath = await editorProvider.mergeMultipleAudioToVideo(
          context,
          combinedVideoPath: currentPath,
        );
        if (mergedAudioPath != null) {
          currentPath = mergedAudioPath;
        }
      }

      // Final step: Move to output path
      await File(cleanPath(currentPath)).copy(cleanPath(outputPath));
      return outputPath;
    } catch (e) {
      print(e);
      rethrow;
    } finally {
      // Cleanup temp files
      // final tempFiles = await Directory(tempDir.path)
      //     .list()
      //     .where((entity) =>
      //         entity.path.endsWith('.mp4') || entity.path.endsWith('.m4a'))
      //     .toList();
      //
      // for (var file in tempFiles) {
      //   await file.delete();
      // }
    }
  }

  static String cleanPath(String path) {
    return path.startsWith('file://') ? path.replaceFirst('file://', '') : path;
  }

  static Future<bool> _cropVideo(
    String inputPath,
    String outputPath,
    Offset minCrop,
    Offset maxCrop,
    double videoWidth,
    double videoHeight,
  ) async {
    final crop =
        await _getCropCommand(minCrop, maxCrop, videoWidth, videoHeight);
    final command = '-i $inputPath -vf $crop -c:a copy $outputPath';
    final session = await FFmpegKit.execute(command);
    final returnCode = await session.getReturnCode();
    if (ReturnCode.isSuccess(returnCode)) {
      return true;
    }
    return false;
  }

  static Future<String> _getCropCommand(Offset minCrop, Offset maxCrop,
      double videoWidth, double videoHeight) async {
    final enddx = videoWidth * maxCrop.dx;
    final enddy = videoHeight * maxCrop.dy;
    final startdx = videoWidth * minCrop.dx;
    final startdy = videoHeight * minCrop.dy;
    return "crop=${enddx - startdx}:${enddy - startdy}:$startdx:$startdy";
  }

  // Add these new methods:
  static Future<bool> _rotateVideo(
    String inputPath,
    String outputPath,
    int rotation,
  ) async {
    final transpose = await _getRotationCommand(rotation);
    final command = '-i $inputPath -vf "$transpose" -c:a copy $outputPath';
    final session = await FFmpegKit.execute(command);
    final returnCode = await session.getReturnCode();
    if (ReturnCode.isSuccess(returnCode)) {
      return true;
    }
    return false;
  }

  static Future<String> _getRotationCommand(int rotation) async {
    final count = rotation / 90;
    if (count <= 0 || count >= 4) return "";

    final List<String> transpose = [];
    for (int i = 0; i < rotation / 90; i++) {
      transpose.add("transpose=2");
    }
    return transpose.isNotEmpty ? transpose.join(',') : "";
  }

  static Future<void> _adjustSpeed(
    String inputPath,
    String outputPath,
    double speed,
  ) async {
    final tempo = 1 / speed;
    final command =
        '-i $inputPath -filter_complex "[0:v]setpts=${tempo}*PTS[v];[0:a]atempo=$speed[a]" -map "[v]" -map "[a]" $outputPath';
    await FFmpegKit.execute(command);
  }

  static Future<bool> _applyTransitions(
    String outputPath,
    TransitionType transition,
    List<VideoTrackModel> videoTracks,
  ) async {
    final tempDir = await getTemporaryDirectory();
    List<String> inputFilesPaths = [];
    String transitionedOutputPath = '';

    for (int i = 0; i < videoTracks.length; i++) {
      if (i == 0) {
        inputFilesPaths.add('-i "${videoTracks[i].processedFile.path}"');
        continue;
      }

      if (i == videoTracks.length - 1) {
        transitionedOutputPath = outputPath;
      } else {
        transitionedOutputPath =
            '${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}_transitioned_video$i.mp4';
      }

      inputFilesPaths.add('-i "${videoTracks[i].processedFile.path}"');
      final result = await _applyTransition(inputFilesPaths,
          transitionedOutputPath, videoTracks[i - 1].endTime, transition.name);
      if (!result) return false;
      inputFilesPaths.clear();
      inputFilesPaths.add('-i "${transitionedOutputPath}"');
    }
    return true;
  }

  static Future<bool> _applyTransition(List inputFilesPaths, String outputPath,
      int offset, String transition) async {
    final inputFilesString = inputFilesPaths.join(' ');
    final command =
        '$inputFilesString -filter_complex "[0:v]tpad=stop_mode=clone:stop_duration=1[pad0];[pad0][1:v]xfade=transition=${transition}:duration=1:offset=$offset[vfade1];[vfade1]format=yuv420p" -c:a copy $outputPath';
    final session = await FFmpegKit.execute(command);
    final returnCode = await session.getReturnCode();
    if (ReturnCode.isSuccess(returnCode)) {
      return true;
    }
    return false;
  }

  static List<String> _exportLogs = []; // Store export logs for debugging

  // Method to get export logs for debugging
  static List<String> getExportLogs() => List.from(_exportLogs);

  /// ✅ NEW: Calculate coordinate system offset for export when crop is applied from start
  /// This ensures consistency between preview and export coordinate systems
  /// When crop is applied from start (e.g., minCrop.dx = 0.0, minCrop.dy = 0.0),
  /// the cropped area gets centered in the preview, creating a coordinate system offset
  static Offset _calculateExportCoordinateSystemOffset({
    required Offset minCrop,
    required Offset maxCrop,
    required double videoWidth,
    required double videoHeight,
    required double containerWidth,
    required double containerHeight,
    int? rotation,
  }) {
    debugPrint('=== Export Coordinate System Offset Calculation ===');
    debugPrint(
        'Crop: (${minCrop.dx}, ${minCrop.dy}) to (${maxCrop.dx}, ${maxCrop.dy})');
    debugPrint('Video dimensions: ${videoWidth}x${videoHeight}');
    debugPrint('Container dimensions: ${containerWidth}x${containerHeight}');
    debugPrint('Rotation: ${rotation ?? 0}°');

    // ✅ ADDED: Detect coordinate system type
    final isPixelCoordinates = maxCrop.dx > 1.0 || maxCrop.dy > 1.0;
    debugPrint(
        'Coordinate system detected: ${isPixelCoordinates ? 'PIXELS' : 'NORMALIZED (0.0 to 1.0)'}');

    // ✅ UNIVERSAL APPROACH: Always calculate offset for ANY crop
    // Every crop gets centered in the preview, so every crop needs offset calculation
    // This handles all scenarios: start, bottom, middle, random positions

    debugPrint('Universal crop offset calculation:');
    debugPrint('  Crop position: left=${minCrop.dx}, top=${minCrop.dy}');
    debugPrint(
        '  Every crop gets centered in preview - offset calculation needed');

    // Calculate how the original video fits in the container
    final originalFitting = _calculateRotationAwareGaps(
      videoWidth: videoWidth,
      videoHeight: videoHeight,
      containerWidth: containerWidth,
      containerHeight: containerHeight,
      rotation: rotation ?? 0,
    );

    final originalPreviewWidth = originalFitting['actualPreviewWidth']!;
    final originalPreviewHeight = originalFitting['actualPreviewHeight']!;
    final originalGapLeft = originalFitting['gapLeft']!;
    final originalGapTop = originalFitting['gapTop']!;

    debugPrint('Original video fitting:');
    debugPrint(
        '  Preview area: ${originalPreviewWidth}x${originalPreviewHeight}');
    debugPrint('  Gaps: left=${originalGapLeft}, top=${originalGapTop}');

    // Calculate the scale factors from video to preview
    final scaleX = originalPreviewWidth / videoWidth;
    final scaleY = originalPreviewHeight / videoHeight;

    // ✅ FIXED: Handle both pixel coordinates and normalized coordinates correctly
    double cropLeftInPreview,
        cropTopInPreview,
        cropWidthInPreview,
        cropHeightInPreview;

    // Check if crop coordinates are in pixel coordinates or normalized coordinates
    // Note: isPixelCoordinates is already defined above

    if (isPixelCoordinates) {
      // Crop coordinates are in pixels - convert to preview coordinates
      cropLeftInPreview = originalGapLeft + (minCrop.dx * scaleX);
      cropTopInPreview = originalGapTop + (minCrop.dy * scaleY);
      cropWidthInPreview = (maxCrop.dx - minCrop.dx) * scaleX;
      cropHeightInPreview = (maxCrop.dy - minCrop.dy) * scaleY;

      debugPrint(
          '  Crop coordinates are in PIXELS - using pixel-based calculation');
    } else {
      // Crop coordinates are normalized (0.0 to 1.0) - convert to preview coordinates
      cropLeftInPreview = originalGapLeft + (minCrop.dx * originalPreviewWidth);
      cropTopInPreview = originalGapTop + (minCrop.dy * originalPreviewHeight);
      cropWidthInPreview = (maxCrop.dx - minCrop.dx) * originalPreviewWidth;
      cropHeightInPreview = (maxCrop.dy - minCrop.dy) * originalPreviewHeight;

      debugPrint(
          '  Crop coordinates are NORMALIZED - using normalized-based calculation');
    }

    debugPrint('Crop in preview:');
    debugPrint(
        '  Position: left=${cropLeftInPreview.toStringAsFixed(2)}, top=${cropTopInPreview.toStringAsFixed(2)}');
    debugPrint(
        '  Size: ${cropWidthInPreview.toStringAsFixed(2)}x${cropHeightInPreview.toStringAsFixed(2)}');
    debugPrint(
        '  Coordinate system: ${isPixelCoordinates ? 'PIXELS' : 'NORMALIZED'}');
    debugPrint(
        '  Scale factors: X=${scaleX.toStringAsFixed(4)}, Y=${scaleY.toStringAsFixed(4)}');

    // ✅ ADDED: Universal crop analysis
    debugPrint('Universal crop analysis:');
    debugPrint('  Crop position: left=${minCrop.dx}, top=${minCrop.dy}');
    debugPrint('  Every crop gets centered - coordinate system offset needed');

    // Now calculate how the cropped video fits within the original preview area
    final cropAspectRatio = cropWidthInPreview / cropHeightInPreview;
    final videoDisplayAspectRatio =
        originalPreviewWidth / originalPreviewHeight;

    double finalCropWidth, finalCropHeight, finalCropLeft, finalCropTop;

    if (cropAspectRatio > videoDisplayAspectRatio) {
      // Crop is wider - fit width, letterbox top/bottom within video area
      finalCropWidth = originalPreviewWidth;
      finalCropHeight = originalPreviewWidth / cropAspectRatio;
      finalCropTop =
          originalGapTop + (originalPreviewHeight - finalCropHeight) / 2.0;
      finalCropLeft = originalGapLeft;
    } else {
      // Crop is taller - fit height, letterbox left/right within video area
      finalCropHeight = originalPreviewHeight;
      finalCropWidth = originalPreviewHeight * cropAspectRatio;
      finalCropLeft =
          originalGapLeft + (originalPreviewWidth - finalCropWidth) / 2.0;
      finalCropTop = originalGapTop;
    }

    debugPrint('Final crop area:');
    debugPrint(
        '  Position: left=${finalCropLeft.toStringAsFixed(2)}, top=${finalCropTop.toStringAsFixed(2)}');
    debugPrint(
        '  Size: ${finalCropWidth.toStringAsFixed(2)}x${finalCropHeight.toStringAsFixed(2)}');

    // ✅ FIXED: Calculate the coordinate system offset for edge crops
    // This handles crops from left edge, top edge, or both
    double expectedCropLeft, expectedCropTop;

    if (isPixelCoordinates) {
      // For pixel coordinates, use scale factors
      expectedCropLeft = originalGapLeft + (minCrop.dx * scaleX);
      expectedCropTop = originalGapTop + (minCrop.dy * scaleY);
    } else {
      // For normalized coordinates, use preview dimensions
      expectedCropLeft = originalGapLeft + (minCrop.dx * originalPreviewWidth);
      expectedCropTop = originalGapTop + (minCrop.dy * originalPreviewHeight);
    }

    // ✅ UNIVERSAL: Always calculate offset for ANY crop
    // Every crop gets centered in the preview, creating a coordinate system shift
    final coordinateOffsetX = finalCropLeft - expectedCropLeft;
    final coordinateOffsetY = finalCropTop - expectedCropTop;

    debugPrint('  Universal offset calculation:');
    debugPrint(
        '    X offset: $finalCropLeft - $expectedCropLeft = $coordinateOffsetX');
    debugPrint(
        '    Y offset: $finalCropTop - $expectedCropTop = $coordinateOffsetY');

    debugPrint('Coordinate system offset calculation:');
    debugPrint(
        '  Using ${isPixelCoordinates ? 'PIXEL' : 'NORMALIZED'} coordinate system');
    debugPrint(
        '  Expected position: left=${expectedCropLeft.toStringAsFixed(2)}, top=${expectedCropTop.toStringAsFixed(2)}');
    debugPrint(
        '  Actual position: left=${finalCropLeft.toStringAsFixed(2)}, top=${finalCropTop.toStringAsFixed(2)}');
    debugPrint(
        '  Offset: X=${coordinateOffsetX.toStringAsFixed(2)}, Y=${coordinateOffsetY.toStringAsFixed(2)}');

    final finalOffset = Offset(coordinateOffsetX, coordinateOffsetY);
    debugPrint(
        'Final coordinate system offset: (${finalOffset.dx}, ${finalOffset.dy})');
    debugPrint('=== End Export Coordinate System Offset Calculation ===');

    return finalOffset;
  }

  // Font-family specific width multipliers for more accurate text dimension calculation
  static double _getFontWidthMultiplier(String fontFamily) {
    switch (fontFamily.toLowerCase()) {
      case 'arial':
      case 'helvetica':
        return 0.55; // Medium width
      case 'times new roman':
      case 'georgia':
        return 0.50; // Narrower serif fonts
      case 'courier new':
        return 0.60; // Monospace - wider
      case 'verdana':
        return 0.58; // Slightly wider
      case 'comic sans ms':
        return 0.52; // Casual font
      case 'impact':
        return 0.45; // Condensed font
      default:
        return 0.55; // Default for unknown fonts
    }
  }

  // Get appropriate font file path based on font family and platform
  static String _getFontFilePath(String fontFamily) {
    if (Platform.isAndroid) {
      switch (fontFamily.toLowerCase()) {
        case 'arial':
        case 'helvetica':
        case 'verdana':
          return '/system/fonts/Roboto-Regular.ttf';
        case 'times new roman':
        case 'georgia':
          return '/system/fonts/NotoSerif-Regular.ttf';
        case 'courier new':
          return '/system/fonts/DroidSansMono.ttf';
        case 'comic sans ms':
          return '/system/fonts/ComingSoon.ttf';
        case 'impact':
          return '/system/fonts/Roboto-Black.ttf';
        default:
          return '/system/fonts/Roboto-Regular.ttf';
      }
    } else {
      // iOS font paths
      switch (fontFamily.toLowerCase()) {
        case 'arial':
          return '/System/Library/Fonts/Arial.ttf';
        case 'helvetica':
          return '/System/Library/Fonts/Helvetica.ttc';
        case 'times new roman':
          return '/System/Library/Fonts/Times.ttc';
        case 'courier new':
          return '/System/Library/Fonts/Courier.ttc';
        case 'verdana':
          return '/System/Library/Fonts/Verdana.ttc';
        case 'georgia':
          return '/System/Library/Fonts/Georgia.ttc';
        default:
          return '/System/Library/Fonts/Arial.ttf';
      }
    }
  }

  // Method to show export logs in a dialog
  static void showExportLogs(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Export Debug Logs'),
        content: Container(
          width: double.maxFinite,
          height: 400,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: _exportLogs
                  .map((log) => Padding(
                        padding: EdgeInsets.symmetric(vertical: 2),
                        child: Text(
                          log,
                          style: TextStyle(
                            fontFamily: 'monospace',
                            fontSize: 12,
                          ),
                        ),
                      ))
                  .toList(),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Close'),
          ),
        ],
      ),
    );
  }

  /// ✅ UPDATED: Now rotation-aware for consistent coordinate mapping
  /// This method now calculates rotation-aware gaps to ensure that text overlay
  /// positioning matches the preview system when videos are rotated.
  static Future<bool> _addTextOverlay(
    BuildContext context,
    String inputPath,
    String outputPath,
    List<TextTrackModel> textTracks,
    double? croppedVideoWidth, {
    double? originalVideoWidth,
    double? originalVideoHeight,
    double? previewHeight,
    Offset? minCrop,
    Offset? maxCrop,
    int?
        rotation, // ✅ ADDED: Rotation parameter for rotation-aware gap calculation
  }) async {
    _exportLogs.add('=== _addTextOverlay START ===');
    _exportLogs.add('Input path: $inputPath');
    _exportLogs.add('Output path: $outputPath');
    _exportLogs.add('Text tracks count: ${textTracks.length}');
    _exportLogs.add('Cropped video width: $croppedVideoWidth');
    _exportLogs.add('Original video width: $originalVideoWidth');
    _exportLogs.add('Original video height: $originalVideoHeight');
    _exportLogs.add('Preview height: $previewHeight');
    _exportLogs.add('Min crop: $minCrop');
    _exportLogs.add('Max crop: $maxCrop');
    _exportLogs
        .add('Rotation: ${rotation ?? 0}°'); // ✅ ADDED: Log rotation parameter

    // ✅ ADDED: Summary log for crop + rotation combination
    if (minCrop != null &&
        maxCrop != null &&
        rotation != null &&
        rotation != 0) {
      debugPrint('=== CROP + ROTATION COMBINATION DETECTED ===');
      debugPrint('This export will handle both crop and rotation operations');
      debugPrint(
          'Crop: (${minCrop.dx.toStringAsFixed(3)}, ${minCrop.dy.toStringAsFixed(3)}) to (${maxCrop.dx.toStringAsFixed(3)}, ${maxCrop.dy.toStringAsFixed(3)})');
      debugPrint('Rotation: ${rotation}°');
      debugPrint(
          'Original dimensions: ${originalVideoWidth ?? 'unknown'}x${originalVideoHeight ?? 'unknown'}');
      debugPrint('=== END CROP + ROTATION SUMMARY ===');
    } else if (minCrop != null && maxCrop != null) {
      debugPrint('=== CROP ONLY DETECTED ===');
      debugPrint('This export will handle crop operation only');
      debugPrint(
          'Crop: (${minCrop.dx.toStringAsFixed(3)}, ${minCrop.dy.toStringAsFixed(3)}) to (${maxCrop.dx.toStringAsFixed(3)}, ${maxCrop.dy.toStringAsFixed(3)})');
      debugPrint('=== END CROP SUMMARY ===');
    } else if (rotation != null && rotation != 0) {
      debugPrint('=== ROTATION ONLY DETECTED ===');
      debugPrint('This export will handle rotation operation only');
      debugPrint('Rotation: ${rotation}°');
      debugPrint('=== END ROTATION SUMMARY ===');
    } else {
      debugPrint('=== NO CROP OR ROTATION DETECTED ===');
      debugPrint('This export will use original video dimensions');
      debugPrint('=== END NO OPERATIONS SUMMARY ===');
    }

    // Verify input file exists
    final inputFile = File(inputPath);
    if (!await inputFile.exists()) {
      _exportLogs.add('ERROR: Input file does not exist: $inputPath');
      return false;
    }
    _exportLogs.add('Input file exists: $inputPath');

    if (textTracks.isEmpty) {
      _exportLogs.add('No text tracks to process');
      return true;
    }

    _exportLogs.clear();

    _exportLogs.add('minCrop: ' +
        (minCrop != null ? '(${minCrop.dx}, ${minCrop.dy})' : 'null') +
        ', maxCrop: ' +
        (maxCrop != null ? '(${maxCrop.dx}, ${maxCrop.dy})' : 'null'));

    // Get video dimensions
    final deviceWidth = MediaQuery.of(context).size.width;
    final previewHeightValue = previewHeight ?? 400.0;

    double videoWidth;
    double actualVideoHeight;
    double exportVideoWidth;
    double exportActualVideoHeight;

    if (croppedVideoWidth != null) {
      debugPrint('=== CROP PATH: Using cropped video dimensions ===');
      // For coordinate calculations, use original dimensions
      videoWidth = originalVideoWidth ?? croppedVideoWidth;
      actualVideoHeight = originalVideoHeight ?? (videoWidth / (16 / 9));

      // ✅ FIXED: Use rotation-aware export dimensions for crop path too
      if (rotation == 90 || rotation == 270) {
        // For 90° and 270° rotation, swap dimensions for export
        // ✅ FIXED: When both crop and rotation are applied, we need to calculate the correct dimensions
        if (originalVideoWidth != null &&
            originalVideoHeight != null &&
            minCrop != null &&
            maxCrop != null) {
          // Calculate the actual cropped dimensions considering rotation
          final cropWidthRatio = maxCrop.dy -
              minCrop.dy; // Use dy difference for width when rotated
          final cropHeightRatio = maxCrop.dx -
              minCrop.dx; // Use dx difference for height when rotated

          // For rotated videos, the cropped dimensions are swapped
          final croppedWidth = originalVideoHeight * cropWidthRatio;
          final croppedHeight = originalVideoWidth * cropHeightRatio;

          exportVideoWidth = croppedWidth;
          exportActualVideoHeight = croppedHeight;

          debugPrint(
              'CROP PATH: [ROTATED] Crop ratios: width=${cropWidthRatio.toStringAsFixed(3)}, height=${cropHeightRatio.toStringAsFixed(3)}');
          debugPrint(
              'CROP PATH: [ROTATED] Cropped dimensions: ${croppedWidth.toStringAsFixed(0)}x${croppedHeight.toStringAsFixed(0)}');
        } else {
          // Fallback to rotated dimensions without crop
          exportVideoWidth = originalVideoHeight ?? croppedVideoWidth;
          exportActualVideoHeight =
              originalVideoWidth ?? (croppedVideoWidth / (16 / 9));
        }
        debugPrint(
            'CROP PATH: [ROTATED] Final export dimensions: ${exportVideoWidth}x${exportActualVideoHeight}');
      } else if (rotation == 180) {
        // For 180° rotation, dimensions stay the same but crop coordinates are inverted
        exportVideoWidth = croppedVideoWidth;
        if (originalVideoWidth != null &&
            originalVideoHeight != null &&
            minCrop != null &&
            maxCrop != null) {
          // Calculate the actual cropped height based on crop parameters
          final cropWidthRatio = maxCrop.dx - minCrop.dx;
          final cropHeightRatio = maxCrop.dy - minCrop.dy;
          final croppedHeight = originalVideoHeight * cropHeightRatio;
          exportActualVideoHeight = croppedHeight;
        } else {
          exportActualVideoHeight = exportVideoWidth / (16 / 9);
        }
        debugPrint(
            'CROP PATH: [180°] Final export dimensions: ${exportVideoWidth}x${exportActualVideoHeight}');
      } else {
        // For 0° rotation, use normal cropped dimensions
        exportVideoWidth = croppedVideoWidth;
        if (originalVideoWidth != null &&
            originalVideoHeight != null &&
            minCrop != null &&
            maxCrop != null) {
          // Calculate the actual cropped height based on crop parameters
          final cropWidthRatio = maxCrop.dx - minCrop.dx;
          final cropHeightRatio = maxCrop.dy - minCrop.dy;
          final croppedHeight = originalVideoHeight * cropHeightRatio;
          exportActualVideoHeight = croppedHeight;
        } else {
          exportActualVideoHeight = exportVideoWidth / (16 / 9);
        }
        debugPrint(
            'CROP PATH: [0°] Final export dimensions: ${exportVideoWidth}x${exportActualVideoHeight}');
      }
    } else if (originalVideoWidth != null && originalVideoHeight != null) {
      debugPrint('=== NO CROP PATH: Using original video dimensions ===');
      videoWidth = originalVideoWidth;
      actualVideoHeight = originalVideoHeight;

      // ✅ FIXED: Use rotation-aware export dimensions
      if (rotation == 90 || rotation == 270) {
        // For 90° and 270° rotation, swap dimensions for export
        exportVideoWidth = originalVideoHeight; // 1280 for 90° rotation
        exportActualVideoHeight = originalVideoWidth; // 720 for 90° rotation
        debugPrint(
            'Using rotated export dimensions: ${exportVideoWidth}x${exportActualVideoHeight}');
      } else {
        // For 0° and 180° rotation, keep original dimensions
        exportVideoWidth = originalVideoWidth;
        exportActualVideoHeight = originalVideoHeight;
        debugPrint(
            'Using original export dimensions: ${exportVideoWidth}x${exportActualVideoHeight}');
      }
    } else {
      debugPrint('ERROR: No video dimensions available');
      return false;
    }

    debugPrint(
        'Coordinate calculation dimensions: ${videoWidth}x${actualVideoHeight}');
    debugPrint(
        'Export dimensions: ${exportVideoWidth}x${exportActualVideoHeight}');
    debugPrint('Device dimensions: ${deviceWidth}x${previewHeightValue}');

    // ✅ ADDED: Log rotation-aware export dimensions
    if (rotation != null && rotation != 0) {
      debugPrint('=== Rotation-Aware Export Dimensions ===');
      debugPrint(
          'Original video dimensions: ${originalVideoWidth}x${originalVideoHeight}');
      debugPrint('Rotation: ${rotation}°');
      debugPrint(
          'Final export dimensions: ${exportVideoWidth}x${exportActualVideoHeight}');
      debugPrint('=== End Rotation-Aware Export Dimensions ===');

      // ✅ ADDED: Additional export logs for rotation
      debugPrint('=== Rotation-Aware Export Dimensions ===');
      debugPrint(
          'Original video dimensions: ${originalVideoWidth}x${originalVideoHeight}');
      debugPrint('Rotation: ${rotation}°');
      debugPrint(
          'Final export dimensions: ${exportVideoWidth}x${exportActualVideoHeight}');
      debugPrint('=== End Rotation-Aware Export Dimensions ===');
    }

    // Use FontScalingHelper for consistent scaling between preview and export
    // Calculate container fitting parameters for consistent scaling
    final containerFitting = FontScalingHelper.calculateContainerFitting(
      videoWidth: exportVideoWidth,
      videoHeight: exportActualVideoHeight,
      containerWidth: deviceWidth,
      containerHeight: previewHeightValue,
    );

    final actualPreviewWidth = containerFitting['actualPreviewWidth']!;
    final actualPreviewHeight = containerFitting['actualPreviewHeight']!;
    final gapLeft = containerFitting['gapLeft']!;
    final gapTop = containerFitting['gapTop']!;

    debugPrint('=== Font Scaling Debug ===');
    debugPrint(
        'Export video dimensions: ${exportVideoWidth}x${exportActualVideoHeight}');
    debugPrint('Container dimensions: ${deviceWidth}x${previewHeightValue}');
    debugPrint(
        'Actual preview area: ${actualPreviewWidth}x${actualPreviewHeight}');
    debugPrint('Gap offsets: gapLeft=$gapLeft, gapTop=$gapTop');
    debugPrint('=== End Font Scaling Debug ===');

    // Convert ALL text to images for consistency and better quality
    List<Map<String, dynamic>> createdImages =
        []; // Store image info with track index
    List<String> imageInputs = []; // Store image input commands
    List<String> overlayFilters = []; // Store overlay filters

    // Process each track individually to avoid variable scope issues
    for (int i = 0; i < textTracks.length; i++) {
      final track = textTracks[i];
      final start = track.trimStartTime;
      final end = track.trimEndTime;

      // Calculate position for this specific track
      double scaledX, scaledY;

      _exportLogs
          .add('Track $i: DEBUG - Processing track with ID: ${track.id}');
      _exportLogs.add('Track $i: DEBUG - Track text: "${track.text}"');
      _exportLogs.add(
          'Track $i: DEBUG - Track position: (${track.position.dx}, ${track.position.dy})');
      _exportLogs.add('Track $i: DEBUG - Track timing: start=$start, end=$end');
      _exportLogs.add('Track $i: DEBUG - Track font size: ${track.fontSize}');
      _exportLogs
          .add('Track $i: DEBUG - Track font family: ${track.fontFamily}');
      _exportLogs.add('Track $i: DEBUG - Track rotation: ${track.rotation}');
      debugPrint(
          'Track $i: DEBUG - Track position: (${track.position.dx}, ${track.position.dy})');
      debugPrint('Track $i: DEBUG - Track timing: start=$start, end=$end');
      debugPrint('Track $i: DEBUG - Track font size: ${track.fontSize}');
      debugPrint('Track $i: DEBUG - Track font family: ${track.fontFamily}');
      debugPrint('Track $i: DEBUG - Track rotation: ${track.rotation}');

      // --- FIXED LOGIC STARTS HERE ---
      debugPrint('Track $i: DEBUG - Checking crop conditions...');
      debugPrint('Track $i: DEBUG - minCrop: $minCrop, maxCrop: $maxCrop');
      _exportLogs.add('Track $i: DEBUG - Checking crop conditions...');
      _exportLogs.add('Track $i: DEBUG - minCrop: $minCrop, maxCrop: $maxCrop');
      _exportLogs.add(
          'Track $i: DEBUG - originalVideoWidth: $originalVideoWidth, originalVideoHeight: $originalVideoHeight');
      debugPrint(
          'Track $i: DEBUG - originalVideoWidth: $originalVideoWidth, originalVideoHeight: $originalVideoHeight');

      // Check if crop represents full video area
      final isFullCrop = minCrop != null &&
          maxCrop != null &&
          minCrop.dx == 0.0 &&
          minCrop.dy == 0.0 &&
          maxCrop.dx == 1.0 &&
          maxCrop.dy == 1.0;
      _exportLogs.add('Track $i: DEBUG - isFullCrop: $isFullCrop');

      if (minCrop != null &&
          maxCrop != null &&
          originalVideoWidth != null &&
          originalVideoHeight != null &&
          // Check if this represents the full video area (no actual crop)
          !isFullCrop) {
        _exportLogs.add('Track $i: DEBUG - Using CROP path');
        _exportLogs.add('Track $i: DEBUG - Rotation: ${rotation ?? 0}°');

        // ✅ ADDED: Summary log for crop + rotation combination
        if (rotation != null && rotation != 0) {
          debugPrint(
              'Track $i: [CROP+ROTATION] Processing crop + rotation combination');
          debugPrint(
              'Track $i: [CROP+ROTATION] Crop: (${minCrop.dx.toStringAsFixed(3)}, ${minCrop.dy.toStringAsFixed(3)}) to (${maxCrop.dx.toStringAsFixed(3)}, ${maxCrop.dy.toStringAsFixed(3)})');
          debugPrint(
              'Track $i: [CROP+ROTATION] Original dimensions: ${originalVideoWidth}x${originalVideoHeight}');
          debugPrint('Track $i: [CROP+ROTATION] Rotation: ${rotation}°');
        } else {
          debugPrint('Track $i: [CROP] Processing crop only (no rotation)');
          debugPrint(
              'Track $i: [CROP] Crop: (${minCrop.dx.toStringAsFixed(3)}, ${minCrop.dy.toStringAsFixed(3)}) to (${maxCrop.dx.toStringAsFixed(3)}, ${maxCrop.dy.toStringAsFixed(3)})');
          debugPrint(
              'Track $i: [CROP] Original dimensions: ${originalVideoWidth}x${originalVideoHeight}');
        }

        // Map position from preview coordinates to cropped video space
        // ✅ FIXED: Use rotation-aware gap calculation for consistent coordinate mapping
        final rotationAwareGaps = _calculateRotationAwareGaps(
          videoWidth: originalVideoWidth,
          videoHeight: originalVideoHeight,
          containerWidth: deviceWidth,
          containerHeight: previewHeightValue,
          rotation: rotation,
        );

        final actualPreviewWidth = rotationAwareGaps['actualPreviewWidth']!;
        final actualPreviewHeight = rotationAwareGaps['actualPreviewHeight']!;
        final gapLeft = rotationAwareGaps['gapLeft']!;
        final gapTop = rotationAwareGaps['gapTop']!;

        debugPrint(
            'Track $i: [CROP] Rotation-aware gaps: left=$gapLeft, top=$gapTop');
        debugPrint(
            'Track $i: [CROP] Rotation-aware preview area: ${actualPreviewWidth}x${actualPreviewHeight}');

        // ✅ FIXED: Use rotation-aware video dimensions for coordinate mapping
        // When video is rotated 90°, effective dimensions are swapped (1280x720 instead of 720x1280)
        final effectiveVideoWidth = (rotation == 90 || rotation == 270)
            ? originalVideoHeight
            : originalVideoWidth;
        final effectiveVideoHeight = (rotation == 90 || rotation == 270)
            ? originalVideoWidth
            : originalVideoHeight;

        debugPrint(
            'Track $i: [CROP] Effective video dimensions for coordinate mapping: ${effectiveVideoWidth}x${effectiveVideoHeight}');

        // ✅ ADDED: Calculate coordinate system offset for export consistency
        final coordinateSystemOffset = _calculateExportCoordinateSystemOffset(
          minCrop: minCrop,
          maxCrop: maxCrop,
          videoWidth: effectiveVideoWidth,
          videoHeight: effectiveVideoHeight,
          containerWidth: deviceWidth,
          containerHeight: previewHeightValue,
          rotation: rotation,
        );

        debugPrint(
            'Track $i: [CROP] Coordinate system offset: (${coordinateSystemOffset.dx}, ${coordinateSystemOffset.dy})');

        // ✅ ADDED: Apply coordinate system offset to text position
        final adjustedTextPosition = Offset(
          track.position.dx - coordinateSystemOffset.dx,
          track.position.dy - coordinateSystemOffset.dy,
        );

        debugPrint(
            'Track $i: [CROP] Original text position: (${track.position.dx}, ${track.position.dy})');
        debugPrint(
            'Track $i: [CROP] Adjusted text position: (${adjustedTextPosition.dx}, ${adjustedTextPosition.dy})');

        // ✅ ADDED: Detailed logging for coordinate transformation debugging
        debugPrint('=== Coordinate Transformation Debug (CROP) ===');
        debugPrint(
            'Track $i: Preview position: (${track.position.dx}, ${track.position.dy})');
        debugPrint(
            'Track $i: Adjusted position: (${adjustedTextPosition.dx}, ${adjustedTextPosition.dy})');
        debugPrint('Track $i: Gap offset: left=$gapLeft, top=$gapTop');
        debugPrint(
            'Track $i: Preview area: ${actualPreviewWidth}x${actualPreviewHeight}');
        debugPrint(
            'Track $i: Effective video dimensions: ${effectiveVideoWidth}x${effectiveVideoHeight}');

        // Convert from ADJUSTED preview coordinates to original video space using FontScalingHelper
        final videoPosition = FontScalingHelper.calculateVideoPosition(
          previewPositionX:
              adjustedTextPosition.dx, // ✅ FIXED: Use adjusted position
          previewPositionY:
              adjustedTextPosition.dy, // ✅ FIXED: Use adjusted position
          videoWidth: effectiveVideoWidth, // ✅ FIXED: Use rotated dimensions
          videoHeight: effectiveVideoHeight, // ✅ FIXED: Use rotated dimensions
          actualPreviewWidth: actualPreviewWidth,
          actualPreviewHeight: actualPreviewHeight,
          gapLeft: gapLeft,
          gapTop: gapTop,
        );

        final originalVideoX = videoPosition.dx;
        final originalVideoY = videoPosition.dy;

        debugPrint(
            'Track $i: Video coordinates: (${originalVideoX.toStringAsFixed(2)}, ${originalVideoY.toStringAsFixed(2)})');
        debugPrint(
            'Track $i: Video position percentages: X=${((originalVideoX / effectiveVideoWidth) * 100).toStringAsFixed(2)}%, Y=${((originalVideoY / effectiveVideoHeight) * 100).toStringAsFixed(2)}%');
        debugPrint('=== End Coordinate Transformation Debug (CROP) ===');

        // ✅ FIXED: Use effective (rotated) video dimensions for crop space calculation
        // When both crop and rotation are applied, we need to handle the coordinate transformation correctly
        double cropSpaceX, cropSpaceY;

        if (rotation != null && rotation != 0) {
          // ✅ NEW: Handle crop + rotation combination correctly
          // The crop coordinates are in the original video space, but we need to account for rotation
          // First, we need to understand how the crop rectangle relates to the rotated video

          debugPrint(
              'Track $i: [CROP+ROTATION] Handling crop + rotation combination');

          // For rotated videos, the crop coordinates need to be interpreted differently
          // When video is rotated 90° or 270°, the crop coordinates are relative to the swapped dimensions
          if (rotation == 90 || rotation == 270) {
            // For 90° and 270° rotation, crop coordinates are relative to the swapped dimensions
            // minCrop.dx represents the left edge in the rotated video (which was originally the top)
            // minCrop.dy represents the top edge in the rotated video (which was originally the left)

            // Calculate crop space considering the rotation
            cropSpaceX = originalVideoX -
                (minCrop.dy * effectiveVideoWidth); // Use dy for X when rotated
            cropSpaceY = originalVideoY -
                (minCrop.dx *
                    effectiveVideoHeight); // Use dx for Y when rotated

            debugPrint('Track $i: [CROP+ROTATION] 90°/270° rotation detected');
            debugPrint(
                'Track $i: [CROP+ROTATION] Using minCrop.dy for X: ${minCrop.dy}');
            debugPrint(
                'Track $i: [CROP+ROTATION] Using minCrop.dx for Y: ${minCrop.dx}');
          } else if (rotation == 180) {
            // For 180° rotation, crop coordinates are inverted
            cropSpaceX =
                originalVideoX - ((1.0 - maxCrop.dx) * effectiveVideoWidth);
            cropSpaceY =
                originalVideoY - ((1.0 - maxCrop.dy) * effectiveVideoHeight);

            debugPrint('Track $i: [CROP+ROTATION] 180° rotation detected');
            debugPrint(
                'Track $i: [CROP+ROTATION] Inverted crop coordinates for 180° rotation');
          } else {
            // No rotation (0°), use normal crop logic
            cropSpaceX = originalVideoX - (minCrop.dx * effectiveVideoWidth);
            cropSpaceY = originalVideoY - (minCrop.dy * effectiveVideoHeight);

            debugPrint('Track $i: [CROP] No rotation, using normal crop logic');
          }

          debugPrint(
              'Track $i: [CROP+ROTATION] Calculated crop space: (${cropSpaceX.toStringAsFixed(2)}, ${cropSpaceY.toStringAsFixed(2)})');
        } else {
          // No rotation, use normal crop logic
          cropSpaceX = originalVideoX - (minCrop.dx * effectiveVideoWidth);
          cropSpaceY = originalVideoY - (minCrop.dy * effectiveVideoHeight);

          debugPrint('Track $i: [CROP] No rotation, using normal crop logic');
        }

        // Map to cropped video coordinates
        // ✅ FIXED: Use the correct crop dimensions for the denominator
        double cropWidthRatio, cropHeightRatio;

        if (rotation != null && rotation != 0) {
          if (rotation == 90 || rotation == 270) {
            // For rotated videos, crop ratios are swapped
            cropWidthRatio =
                maxCrop.dy - minCrop.dy; // Use dy difference for width
            cropHeightRatio =
                maxCrop.dx - minCrop.dx; // Use dx difference for height
          } else if (rotation == 180) {
            // For 180° rotation, crop ratios are inverted
            cropWidthRatio = maxCrop.dx - minCrop.dx;
            cropHeightRatio = maxCrop.dy - minCrop.dy;
          } else {
            // No rotation, use normal ratios
            cropWidthRatio = maxCrop.dx - minCrop.dx;
            cropHeightRatio = maxCrop.dy - minCrop.dy;
          }
        } else {
          // No rotation, use normal ratios
          cropWidthRatio = maxCrop.dx - minCrop.dx;
          cropHeightRatio = maxCrop.dy - minCrop.dy;
        }

        final croppedVideoX =
            cropSpaceX / (cropWidthRatio * effectiveVideoWidth);
        final croppedVideoY =
            cropSpaceY / (cropHeightRatio * effectiveVideoHeight);

        debugPrint(
            'Track $i: [CROP] Crop ratios: width=${cropWidthRatio.toStringAsFixed(3)}, height=${cropHeightRatio.toStringAsFixed(3)}');
        debugPrint(
            'Track $i: [CROP] Cropped video coordinates: (${croppedVideoX.toStringAsFixed(3)}, ${croppedVideoY.toStringAsFixed(3)})');

        // ✅ ADDED: Validate crop coordinates to prevent invalid values
        if (croppedVideoX.isNaN ||
            croppedVideoX.isInfinite ||
            croppedVideoY.isNaN ||
            croppedVideoY.isInfinite) {
          debugPrint(
              'Track $i: [CROP] ERROR: Invalid crop coordinates detected!');
          debugPrint(
              'Track $i: [CROP] cropSpaceX: $cropSpaceX, cropSpaceY: $cropSpaceY');
          debugPrint(
              'Track $i: [CROP] effectiveVideoWidth: $effectiveVideoWidth, effectiveVideoHeight: $effectiveVideoHeight');
          debugPrint(
              'Track $i: [CROP] cropWidthRatio: $cropWidthRatio, cropHeightRatio: $cropHeightRatio');

          // Use safe fallback coordinates
          final safeX = 0.1; // 10% from left
          final safeY = 0.1; // 10% from top
          scaledX = safeX * exportVideoWidth + 20;
          scaledY = safeY * exportActualVideoHeight;

          debugPrint(
              'Track $i: [CROP] Using safe fallback coordinates: (${scaledX.toStringAsFixed(2)}, ${scaledY.toStringAsFixed(2)})');
        } else if (croppedVideoX < 0 ||
            croppedVideoX > 1 ||
            croppedVideoY < 0 ||
            croppedVideoY > 1) {
          // ✅ ADDED: Validate that crop coordinates are within valid range [0, 1]
          debugPrint(
              'Track $i: [CROP] WARNING: Crop coordinates outside valid range [0, 1]');
          debugPrint(
              'Track $i: [CROP] croppedVideoX: $croppedVideoX, croppedVideoY: $croppedVideoY');

          // Clamp coordinates to valid range
          final clampedX = croppedVideoX.clamp(0.0, 1.0);
          final clampedY = croppedVideoY.clamp(0.0, 1.0);

          scaledX = (clampedX * exportVideoWidth) + 20;
          scaledY = clampedY * exportActualVideoHeight;

          debugPrint(
              'Track $i: [CROP] Clamped coordinates: (${clampedX.toStringAsFixed(3)}, ${clampedY.toStringAsFixed(3)})');
          debugPrint(
              'Track $i: [CROP] Final export position: (${scaledX.toStringAsFixed(2)}, ${scaledY.toStringAsFixed(2)})');
        } else {
          // Scale to final export coordinates using export dimensions
          scaledX = (croppedVideoX * exportVideoWidth) +
              20; // Add buffer of 20 to x position
          scaledY = croppedVideoY * exportActualVideoHeight;
        }

        debugPrint('Track $i: Final export calculation (CROP):');
        debugPrint(
            '  X: $croppedVideoX * $exportVideoWidth + 20 = ${scaledX.toStringAsFixed(2)}');
        debugPrint(
            '  Y: $croppedVideoY * $exportActualVideoHeight = ${scaledY.toStringAsFixed(2)}');

        // ✅ ADDED: Final summary log for crop path
        debugPrint('Track $i: [CROP] Final export calculation summary:');
        debugPrint(
            'Track $i: [CROP] - Coordinate system offset: (${coordinateSystemOffset.dx.toStringAsFixed(2)}, ${coordinateSystemOffset.dy.toStringAsFixed(2)})');
        debugPrint(
            'Track $i: [CROP] - Original text position: (${track.position.dx.toStringAsFixed(2)}, ${track.position.dy.toStringAsFixed(2)})');
        debugPrint(
            'Track $i: [CROP] - Adjusted text position: (${adjustedTextPosition.dx.toStringAsFixed(2)}, ${adjustedTextPosition.dy.toStringAsFixed(2)})');
        debugPrint(
            'Track $i: [CROP] - Cropped video coords: (${croppedVideoX.toStringAsFixed(3)}, ${croppedVideoY.toStringAsFixed(3)})');
        debugPrint(
            'Track $i: [CROP] - Export dimensions: ${exportVideoWidth}x${exportActualVideoHeight}');
        debugPrint(
            'Track $i: [CROP] - Final export position: (${scaledX.toStringAsFixed(2)}, ${scaledY.toStringAsFixed(2)})');
        if (rotation != null && rotation != 0) {
          debugPrint('Track $i: [CROP] - Applied rotation: ${rotation}°');
          debugPrint(
              'Track $i: [CROP] - Effective video dimensions: ${effectiveVideoWidth}x${effectiveVideoHeight}');
        }

        // ✅ ADDED: Validate the overall coordinate transformation
        _validateCoordinateTransformation(
          trackIndex: i,
          originalPosition: track.position,
          adjustedPosition:
              adjustedTextPosition, // ✅ NEW: Include adjusted position
          coordinateSystemOffset:
              coordinateSystemOffset, // ✅ NEW: Include coordinate system offset
          videoPosition: videoPosition,
          cropSpace: Offset(cropSpaceX, cropSpaceY),
          croppedVideoCoords: Offset(croppedVideoX, croppedVideoY),
          finalExportPosition: Offset(scaledX, scaledY),
          rotation: rotation,
          exportDimensions: Size(exportVideoWidth, exportActualVideoHeight),
        );

        _exportLogs.add(
            'Track $i: (crop applied) preview position: (${track.position.dx}, ${track.position.dy})');
        _exportLogs.add(
            'Track $i: (crop applied) coordinate system offset: (${coordinateSystemOffset.dx}, ${coordinateSystemOffset.dy})');
        _exportLogs.add(
            'Track $i: (crop applied) adjusted position: (${adjustedTextPosition.dx}, ${adjustedTextPosition.dy})');
        _exportLogs.add(
            'Track $i: (crop applied) original video position: (${originalVideoX}, ${originalVideoY})');
        _exportLogs.add(
            'Track $i: (crop applied) crop space: (${cropSpaceX}, ${cropSpaceY})');
        _exportLogs.add(
            'Track $i: (crop applied) cropped video space: (${croppedVideoX}, ${croppedVideoY})');
        _exportLogs.add(
            'Track $i: (crop applied) final export: (${scaledX.toStringAsFixed(2)}, ${scaledY.toStringAsFixed(2)})');
        _exportLogs.add(
            'Track $i: (crop applied) DEBUG - minCrop: $minCrop, maxCrop: $maxCrop');
        _exportLogs.add(
            'Track $i: (crop applied) DEBUG - originalVideoWidth: $originalVideoWidth, originalVideoHeight: $originalVideoHeight');

        // ✅ ADDED: Additional logging for crop + rotation combination
        if (rotation != null && rotation != 0) {
          debugPrint('Track $i: [CROP+ROTATION] Final calculation summary:');
          debugPrint(
              'Track $i: [CROP+ROTATION] - Coordinate system offset: (${coordinateSystemOffset.dx.toStringAsFixed(2)}, ${coordinateSystemOffset.dy.toStringAsFixed(2)})');
          debugPrint(
              'Track $i: [CROP+ROTATION] - Preview position: (${track.position.dx.toStringAsFixed(2)}, ${track.position.dy.toStringAsFixed(2)})');
          debugPrint(
              'Track $i: [CROP+ROTATION] - Adjusted position: (${adjustedTextPosition.dx.toStringAsFixed(2)}, ${adjustedTextPosition.dy.toStringAsFixed(2)})');
          debugPrint(
              'Track $i: [CROP+ROTATION] - Video coordinates: (${originalVideoX.toStringAsFixed(2)}, ${originalVideoY.toStringAsFixed(2)})');
          debugPrint(
              'Track $i: [CROP+ROTATION] - Crop space: (${cropSpaceX.toStringAsFixed(2)}, ${cropSpaceY.toStringAsFixed(2)})');
          debugPrint(
              'Track $i: [CROP+ROTATION] - Cropped video coords: (${croppedVideoX.toStringAsFixed(3)}, ${croppedVideoY.toStringAsFixed(3)})');
          debugPrint(
              'Track $i: [CROP+ROTATION] - Export dimensions: ${exportVideoWidth}x${exportActualVideoHeight}');
          debugPrint(
              'Track $i: [CROP+ROTATION] - Final export position: (${scaledX.toStringAsFixed(2)}, ${scaledY.toStringAsFixed(2)})');
        }

        debugPrint(
            'Track $i: (crop applied) DEBUG - effectiveVideoWidth: $effectiveVideoWidth, effectiveVideoHeight: $effectiveVideoHeight');
        debugPrint(
            'Track $i: (crop applied) DEBUG - videoWidth: $videoWidth, actualVideoHeight: $actualVideoHeight');
        debugPrint(
            'Track $i: (crop applied) DEBUG - videoWidth: $videoWidth, actualVideoHeight: $actualVideoHeight');
      } else {
        _exportLogs.add('Track $i: DEBUG - Using NO CROP path');
        debugPrint('Track $i: DEBUG - Rotation: ${rotation ?? 0}°');
        // No crop, use WYSIWYG approach to account for letterboxing
        // ✅ FIXED: Use rotation-aware gap calculation for consistent coordinate mapping
        final rotationAwareGaps = _calculateRotationAwareGaps(
          videoWidth: originalVideoWidth!,
          videoHeight: originalVideoHeight!,
          containerWidth: deviceWidth,
          containerHeight: previewHeightValue,
          rotation: rotation,
        );

        final actualPreviewWidth = rotationAwareGaps['actualPreviewWidth']!;
        final actualPreviewHeight = rotationAwareGaps['actualPreviewHeight']!;
        final gapLeft = rotationAwareGaps['gapLeft']!;
        final gapTop = rotationAwareGaps['gapTop']!;
        debugPrint('Track $i: Rotation-aware gaps: left=$gapLeft, top=$gapTop');
        debugPrint(
            'Track $i:Rotation-aware preview area: ${actualPreviewWidth}x${actualPreviewHeight}');

        _exportLogs.add(
            'Track $i: [WYSIWYG] gapLeft=$gapLeft, gapTop=$gapTop, actualPreviewWidth=$actualPreviewWidth, actualPreviewHeight=$actualPreviewHeight');

        // ✅ FIXED: Use rotation-aware video dimensions for coordinate mapping
        // When video is rotated 90°, effective dimensions are swapped (1280x720 instead of 720x1280)
        final effectiveVideoWidth = (rotation == 90 || rotation == 270)
            ? originalVideoHeight
            : originalVideoWidth;
        final effectiveVideoHeight = (rotation == 90 || rotation == 270)
            ? originalVideoWidth
            : originalVideoHeight;

        debugPrint(
            'Track $i: Effective video dimensions for coordinate mapping: ${effectiveVideoWidth}x${effectiveVideoHeight}');

        // ✅ ADDED: Detailed logging for coordinate transformation debugging
        debugPrint('=== Coordinate Transformation Debug ===');
        debugPrint(
            'Track $i: Preview position: (${track.position.dx}, ${track.position.dy})');
        debugPrint('Track $i: Gap offset: left=$gapLeft, top=$gapTop');
        debugPrint(
            'Track $i: Preview area: ${actualPreviewWidth}x${actualPreviewHeight}');
        debugPrint(
            'Track $i: Effective video dimensions: ${effectiveVideoWidth}x${effectiveVideoHeight}');

        // Map overlay position from preview coordinates to video coordinates using FontScalingHelper
        final videoPosition = FontScalingHelper.calculateVideoPosition(
          previewPositionX: track.position.dx,
          previewPositionY: track.position.dy,
          videoWidth: effectiveVideoWidth, // ✅ FIXED: Use rotated dimensions
          videoHeight: effectiveVideoHeight, // ✅ FIXED: Use rotated dimensions
          actualPreviewWidth: actualPreviewWidth,
          actualPreviewHeight: actualPreviewHeight,
          gapLeft: gapLeft,
          gapTop: gapTop,
        );

        final originalVideoX = videoPosition.dx;
        final originalVideoY = videoPosition.dy;

        debugPrint(
            'Track $i: Video coordinates: (${originalVideoX.toStringAsFixed(2)}, ${originalVideoY.toStringAsFixed(2)})');
        debugPrint(
            'Track $i: Video position percentages: X=${((originalVideoX / effectiveVideoWidth) * 100).toStringAsFixed(2)}%, Y=${((originalVideoY / effectiveVideoHeight) * 100).toStringAsFixed(2)}%');
        debugPrint('=== End Coordinate Transformation Debug ===');

        _exportLogs.add(
            'Track $i: position.dx=${track.position.dx}, position.dy=${track.position.dy}');
        _exportLogs.add(
            'Track $i: original video position: (${originalVideoX}, ${originalVideoY})');

        // ✅ FIXED: Use effective (rotated) video dimensions for percentage calculation
        final startPercentX = originalVideoX / effectiveVideoWidth;
        final startPercentY = originalVideoY / effectiveVideoHeight;

        _exportLogs.add(
            'Track $i: startPercentX=$startPercentX, startPercentY=$startPercentY');
        debugPrint(
            'Track $i: Export dimensions: ${exportVideoWidth}x${exportActualVideoHeight}');

        scaledX = (startPercentX * exportVideoWidth) +
            10; // Add buffer of 20 to x position
        scaledY = startPercentY * exportActualVideoHeight;

        debugPrint('Track $i: Final export calculation:');
        debugPrint(
            '  X: $startPercentX * $exportVideoWidth + 10 = ${scaledX.toStringAsFixed(2)}');
        debugPrint(
            '  Y: $startPercentY * $exportActualVideoHeight = ${scaledY.toStringAsFixed(2)}');

        _exportLogs
            .add('Track $i: (no crop) final export: (${scaledX}, ${scaledY})');
        debugPrint(
            'Track $i: (no crop) DEBUG - originalVideoWidth: $originalVideoWidth, originalVideoHeight: $originalVideoHeight');
        debugPrint(
            'Track $i: (no crop) DEBUG - effectiveVideoWidth: $effectiveVideoWidth, effectiveVideoHeight: $effectiveVideoHeight');
        debugPrint(
            'Track $i: (no crop) DEBUG - videoWidth: $videoWidth, actualVideoHeight: $actualVideoHeight');
      }

      // Validate coordinates to prevent crashes
      if (scaledX.isNaN ||
          scaledX.isInfinite ||
          scaledY.isNaN ||
          scaledY.isInfinite) {
        _exportLogs.add(
            'Track $i: Invalid coordinates calculated (NaN/infinite), skipping track');
        continue; // Skip this track only if truly invalid
      }

      // Clamp coordinates to video bounds (allow slight overflow for clipping)
      final originalScaledX = scaledX;
      final originalScaledY = scaledY;
      scaledX = scaledX.clamp(
          -50.0, exportVideoWidth + 50.0); // Use export dimensions for clamping
      scaledY = scaledY.clamp(-50.0,
          exportActualVideoHeight + 50.0); // Use export dimensions for clamping

      // Log if coordinates were adjusted
      if (originalScaledX != scaledX || originalScaledY != scaledY) {
        _exportLogs.add(
            'Track $i: Coordinates adjusted from (${originalScaledX.toStringAsFixed(2)}, ${originalScaledY.toStringAsFixed(2)}) to (${scaledX.toStringAsFixed(2)}, ${scaledY.toStringAsFixed(2)})');
      }

      // Handle rotated text with image conversion
      bool isRotated = (track.rotation ?? 0) != 0;
      if (isRotated) {
        _exportLogs
            .add('Track $i: Processing rotated text with image conversion');
      }

      // Calculate font size using FontScalingHelper for this specific track
      // Step 1: Calculate preview font size first
      final previewFontSize = FontScalingHelper.calculatePreviewFontSize(
        baseFontSize: track.fontSize,
        videoWidth: exportVideoWidth,
        videoHeight: exportActualVideoHeight,
        containerWidth: deviceWidth,
        containerHeight: previewHeightValue,
      );

      // Step 2: Calculate export font size using the same scaling logic as preview
      final scaledFontSize = FontScalingHelper.calculateExportFontSize(
        previewFontSize: previewFontSize,
        videoWidth: exportVideoWidth,
        actualPreviewWidth: actualPreviewWidth,
      );

      // Handle text wrapping using FontScalingHelper
      final previewBoundaryBuffer = 10.0; // Buffer used in preview
      final exportBoundaryBuffer =
          FontScalingHelper.calculateExportBoundaryBuffer(
        previewBoundaryBuffer: previewBoundaryBuffer,
        videoWidth: exportVideoWidth,
        actualPreviewWidth: actualPreviewWidth,
      );

      // Calculate available width based on export dimensions for proper text wrapping
      // This ensures text wrapping uses the actual cropped video dimensions
      final availableWidth = exportVideoWidth - scaledX - exportBoundaryBuffer;
      final availableHeight = exportActualVideoHeight - scaledY;

      _exportLogs.add('=== Export Font Scaling Debug ===');
      _exportLogs.add(
          'Track $i: previewFontSize=$previewFontSize, scaledFontSize=$scaledFontSize');
      _exportLogs.add(
          'Track $i: availableWidth=$availableWidth, availableHeight=$availableHeight');
      _exportLogs.add('Track $i: scaledX=$scaledX, scaledY=$scaledY');
      _exportLogs.add(
          'Track $i: coordinate dimensions: ${videoWidth}x${actualVideoHeight}');
      _exportLogs.add(
          'Track $i: export dimensions: ${exportVideoWidth}x${exportActualVideoHeight}');
      _exportLogs.add(
          'Track $i: previewBoundaryBuffer=$previewBoundaryBuffer, exportBoundaryBuffer=$exportBoundaryBuffer');

      // Verify mathematical consistency
      final maxX = gapLeft + actualPreviewWidth;
      final previewRatio =
          previewFontSize / (maxX - track.position.dx - previewBoundaryBuffer);
      final exportRatio = scaledFontSize / availableWidth;
      _exportLogs.add(
          'Track $i: previewRatio=$previewRatio, exportRatio=$exportRatio');
      _exportLogs.add(
          'Track $i: Ratio difference: ${(previewRatio - exportRatio).abs()}');
      _exportLogs.add('=== End Export Font Scaling Debug ===');

      TextStyle textStyle = TextStyle(
        fontSize: scaledFontSize,
        fontFamily: track.fontFamily,
        height: 1.0, // Use consistent line height to match preview
      );

      List<String> wrappedLines = TextAutoWrapHelper.wrapTextToFit(
        track.text,
        availableWidth,
        availableHeight,
        textStyle,
      );

      _exportLogs
          .add('Track $i: Text wrapping result: ${wrappedLines.length} lines');
      _exportLogs.add('Track $i: Wrapped lines: ${wrappedLines.join(' | ')}');

      // Create image for this specific track
      final tempDir = await getTemporaryDirectory();
      final imagePath =
          '${tempDir.path}/text_overlay_${track.id}_${DateTime.now().millisecondsSinceEpoch}.png';

      try {
        _exportLogs.add('Track $i: Creating image for text: "${track.text}"');
        _exportLogs.add('Track $i: Image path: $imagePath');
        _exportLogs.add(
            'Track $i: Font size: $scaledFontSize, Font family: ${track.fontFamily}');
        _exportLogs.add(
            'Track $i: Available width: $availableWidth, Available height: $availableHeight');

        await _createTextImage(
          text: track.text,
          fontFamily: track.fontFamily,
          fontSize: scaledFontSize,
          color: track.textColor,
          rotation: track.rotation ?? 0,
          maxWidth: availableWidth,
          maxHeight: availableHeight,
          outputPath: imagePath,
        );

        // Verify the image file was created
        final imageFile = File(imagePath);
        if (!await imageFile.exists()) {
          _exportLogs.add('Track $i: Failed to create image file: $imagePath');
          continue;
        }

        final fileSize = await imageFile.length();
        _exportLogs
            .add('Track $i: Created image file: $imagePath ($fileSize bytes)');

        if (fileSize == 0) {
          _exportLogs.add('Track $i: Warning: Image file is empty (0 bytes)');
        }

        // Calculate adjusted position for rotated text using the improved center-based approach
        double adjustedX = scaledX;
        double adjustedY = scaledY;

        debugPrint('Track $i: DEBUG - isRotated: $isRotated');
        debugPrint('Track $i: DEBUG - track.rotation: ${track.rotation}');

        // If text is rotated, adjust the position to account for the center-based rotation
        if (isRotated) {
          // Calculate the original text dimensions
          final textStyle = TextStyle(
            fontSize: scaledFontSize,
            fontFamily: track.fontFamily,
            height: 1.0, // Use consistent line height to match preview
          );

          final textPainter = TextPainter(
            text: TextSpan(
              text: wrappedLines.join('\n'),
              style: textStyle,
            ),
            textDirection: TextDirection.ltr,
            textAlign: TextAlign.left,
          );
          textPainter.layout(maxWidth: availableWidth);

          final originalTextWidth = textPainter.width;
          final originalTextHeight = textPainter.height;

          debugPrint('Track $i: DEBUG - originalTextWidth: $originalTextWidth');
          debugPrint(
              'Track $i: DEBUG - originalTextHeight: $originalTextHeight');

          // Calculate the bounding box size for the rotated text
          final angleRad = (track.rotation ?? 0) * 3.141592653589793 / 180.0;
          final cosAngle = math.cos(angleRad).abs();
          final sinAngle = math.sin(angleRad).abs();

          // Calculate the bounding box dimensions when rotating around the center
          final rotatedWidth =
              originalTextWidth * cosAngle + originalTextHeight * sinAngle;
          final rotatedHeight =
              originalTextWidth * sinAngle + originalTextHeight * cosAngle;

          debugPrint('Track $i: DEBUG - angleRad: $angleRad');
          debugPrint(
              'Track $i: DEBUG - cosAngle: $cosAngle, sinAngle: $sinAngle');
          debugPrint(
              'Track $i: DEBUG - rotatedWidth: $rotatedWidth, rotatedHeight: $rotatedHeight');

          // IMPROVED POSITIONING LOGIC:
          // Since we now paint text at the center of the canvas and rotate around the center,
          // we need to compensate for the fact that the image's top-left corner should align
          // with the desired text position (scaledX, scaledY)

          // The key insight: when text is painted at center and rotated around center,
          // the image's top-left corner is offset by half the rotated bounding box dimensions
          // from the original text position

          // Calculate the offset from the center of the rotated bounding box to its top-left corner
          final offsetX = rotatedWidth / 2.0;
          final offsetY = rotatedHeight / 2.0;

          debugPrint('Track $i: DEBUG - offsetX: $offsetX, offsetY: $offsetY');

          // Adjust the position so that the image's top-left corner aligns with the desired text position
          // This compensates for the center-based rotation approach
          adjustedX = scaledX - offsetX * 0.1;
          adjustedY = scaledY - offsetY;

          debugPrint(
              'Track $i: IMPROVED Rotation adjustment - Using center-based rotation');
          debugPrint(
              'Track $i: IMPROVED Rotation adjustment - Original text: ${originalTextWidth}x${originalTextHeight}');
          debugPrint(
              'Track $i: IMPROVED Rotation adjustment - Rotated bounding box: ${rotatedWidth}x${rotatedHeight}');
          debugPrint(
              'Track $i: IMPROVED Rotation adjustment - Center offsets: offsetX=${offsetX}, offsetY=${offsetY}');
          debugPrint(
              'Track $i: IMPROVED Rotation adjustment - Position: (${scaledX}, ${scaledY}) -> (${adjustedX}, ${adjustedY})');

          // Dispose TextPainter to free memory
          textPainter.dispose();
        }

        // Store the successfully created image info with track index
        createdImages.add({
          'path': imagePath,
          'trackIndex': i,
          'position': Offset(adjustedX, adjustedY),
          'timing': {'start': start, 'end': end},
          'text': track.text,
        });

        // Build image input command with timing
        final imageInput = '-i "$imagePath" -t ${end - start}';
        imageInputs.add(imageInput);

        // Build overlay filter (simplified approach)
        final imageIndex = i + 1; // Image inputs start at index 1
        final outputLabel = i < textTracks.length - 1 ? '[v${i + 1}]' : '';
        final currentLabel = i == 0 ? '0:v' : 'v$i';

        final filter = '[$currentLabel][$imageIndex:v]overlay='
            'x=${adjustedX.toStringAsFixed(0)}:'
            'y=${adjustedY.toStringAsFixed(0)}:'
            'enable=\'between(t,$start,$end)\'$outputLabel';

        overlayFilters.add(filter);
        debugPrint(
            'Track $i: Added image overlay at (${adjustedX.toStringAsFixed(0)}, ${adjustedY.toStringAsFixed(0)})');
        _exportLogs.add(
            'Track $i: Added image overlay at (${adjustedX.toStringAsFixed(0)}, ${adjustedY.toStringAsFixed(0)})');
        _exportLogs.add('Track $i: Filter: $filter');
        _exportLogs.add(
            'Track $i: Current label: $currentLabel, Next label: ${outputLabel.isNotEmpty ? 'v${i + 1}' : 'final'}');
        _exportLogs.add(
            'Track $i: Timing: start=$start, end=$end (duration: ${end - start}s)');
        _exportLogs.add('Track $i: Text: "${track.text}"');
        _exportLogs.add(
            'Track $i: Position: (${track.position.dx}, ${track.position.dy}) -> (${adjustedX.toStringAsFixed(0)}, ${adjustedY.toStringAsFixed(0)})');

        // Additional logging for rotation debugging
        if (isRotated) {
          _exportLogs.add(
              'Track $i: ROTATION DEBUG - Original position: (${track.position.dx}, ${track.position.dy})');
          _exportLogs.add(
              'Track $i: ROTATION DEBUG - Scaled position: (${scaledX.toStringAsFixed(2)}, ${scaledY.toStringAsFixed(2)})');
          _exportLogs.add(
              'Track $i: ROTATION DEBUG - Final adjusted position: (${adjustedX.toStringAsFixed(2)}, ${adjustedY.toStringAsFixed(2)})');
          _exportLogs.add(
              'Track $i: ROTATION DEBUG - Using center-based rotation approach');
        }
        debugPrint('Track $i: Filter: $filter');
        debugPrint(
            'Track $i: Current label: $currentLabel, Next label: ${outputLabel.isNotEmpty ? 'v${i + 1}' : 'final'}');
        debugPrint(
            'Track $i: Timing: start=$start, end=$end (duration: ${end - start}s)');
        debugPrint('Track $i: Text: "${track.text}"');
        debugPrint(
            'Track $i: Position: (${track.position.dx}, ${track.position.dy}) -> (${adjustedX.toStringAsFixed(0)}, ${adjustedY.toStringAsFixed(0)})');
      } catch (e) {
        _exportLogs.add('Track $i: Error creating image: $e');
        _exportLogs.add('Track $i: Stack trace: ${StackTrace.current}');
        debugPrint('Track $i: Error creating image: $e');
        debugPrint('Track $i: Stack trace: ${StackTrace.current}');
        continue;
      }
    }

    if (overlayFilters.isEmpty) {
      _exportLogs.add('No valid text tracks to process');
      return true;
    }

    // Build FFmpeg command with image inputs and timing for all text overlays
    List<String> inputs = ['-i "$inputPath"'];
    List<String> imagePaths = [];

    // Add image inputs with timing
    for (int i = 0; i < createdImages.length; i++) {
      final imageInfo = createdImages[i];
      final imagePath = imageInfo['path'] as String;
      final trackIndex = imageInfo['trackIndex'] as int;
      final position = imageInfo['position'] as Offset;
      final timing = imageInfo['timing'] as Map<String, double>;
      final text = imageInfo['text'] as String;
      final duration = timing['end']! - timing['start']!;

      final imageFile = File(imagePath);
      if (await imageFile.exists()) {
        // Add image input with -t before the -i to control image duration
        inputs.add('-t $duration -i "$imagePath"');
        imagePaths.add(imagePath);
        _exportLogs.add(
            'Image $i (Track $trackIndex): Added image input with -t: $imagePath (duration: ${duration}s)');
        _exportLogs.add(
            'Image $i (Track $trackIndex): Position: (${position.dx.toStringAsFixed(0)}, ${position.dy.toStringAsFixed(0)})');
        _exportLogs.add(
            'Image $i (Track $trackIndex): Timing: ${timing['start']}s to ${timing['end']}s');
        _exportLogs.add('Image $i (Track $trackIndex): Text: "$text"');
        debugPrint(
            'Image $i (Track $trackIndex): Added image input with -t: $imagePath (duration: ${duration}s)');
        debugPrint(
            'Image $i (Track $trackIndex): Position: (${position.dx.toStringAsFixed(0)}, ${position.dy.toStringAsFixed(0)})');
        debugPrint(
            'Image $i (Track $trackIndex): Timing: ${timing['start']}s to ${timing['end']}s');
        debugPrint('Image $i (Track $trackIndex): Text: "$text"');
      } else {
        _exportLogs.add(
            'Image $i (Track $trackIndex): Image file not found, skipping: $imagePath');
      }
    }

    if (imagePaths.isEmpty) {
      _exportLogs
          .add('No valid image files found, text overlay processing failed');
      return false; // Return false instead of true when no images were created
    }

    final filterComplex = overlayFilters.join(';');

    // Don't limit the output video duration - let it use the original video duration
    final command = '${inputs.join(' ')} -filter_complex "$filterComplex" '
        '-c:v libx264 -preset ultrafast -c:a copy "$outputPath"';

    _exportLogs.add('--- FFmpeg Command (with timed images) ---');
    _exportLogs.add('Command: $command');
    _exportLogs.add('Filter complex: $filterComplex');
    _exportLogs.add('Text tracks count: ${textTracks.length}');
    _exportLogs.add('Image files count: ${imagePaths.length}');
    _exportLogs.add('Image inputs with timing: ${inputs.length - 1}');
    _exportLogs.add('Video duration: Preserved from original video');
    debugPrint('--- FFmpeg Command (with timed images) ---');
    debugPrint('Command: $command');
    debugPrint('Filter complex: $filterComplex');
    debugPrint('Text tracks count: ${textTracks.length}');

    try {
      _exportLogs.add('Executing FFmpeg command...');
      debugPrint('Executing FFmpeg command...');
      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();

      if (ReturnCode.isSuccess(returnCode)) {
        _exportLogs.add('Success: Text overlay applied successfully');
        debugPrint('Success: Text overlay applied successfully');

        // Clean up temporary image files
        for (String imagePath in imagePaths) {
          try {
            final imageFile = File(imagePath);
            if (await imageFile.exists()) {
              await imageFile.delete();
              _exportLogs.add('Cleaned up image file: $imagePath');
            }
          } catch (e) {
            _exportLogs.add('Failed to clean up image file: $imagePath - $e');
          }
        }

        // Verify the output file exists
        final outputFile = File(outputPath);
        if (await outputFile.exists()) {
          _exportLogs.add('Output file exists: $outputPath');
          return true;
        } else {
          _exportLogs.add(
              'Error: FFmpeg succeeded but output file does not exist: $outputPath');
          return false;
        }
      } else {
        _exportLogs.add(
            'Error: FFmpeg execution failed with return code: $returnCode');
        debugPrint(
            'Error: FFmpeg execution failed with return code: $returnCode');
        final output = await session.getOutput();
        final logs = await session.getLogs();
        _exportLogs.add('FFmpeg output: $output');
        _exportLogs.add(
            'FFmpeg logs: ${logs.map((log) => log.getMessage()).join('\n')}');
        debugPrint('FFmpeg output: $output');
        debugPrint(
            'FFmpeg logs: ${logs.map((log) => log.getMessage()).join('\n')}');

        // Clean up temporary image files on failure
        for (String imagePath in imagePaths) {
          try {
            final imageFile = File(imagePath);
            if (await imageFile.exists()) {
              await imageFile.delete();
              _exportLogs.add('Cleaned up image file on failure: $imagePath');
            }
          } catch (e) {
            _exportLogs.add(
                'Failed to clean up image file on failure: $imagePath - $e');
          }
        }

        return false;
      }
    } catch (e) {
      _exportLogs.add('Exception during FFmpeg execution: $e');
      _exportLogs.add('Stack trace: ${StackTrace.current}');
      debugPrint('Exception during FFmpeg execution: $e');
      debugPrint('Stack trace: ${StackTrace.current}');

      // Clean up temporary image files on exception
      for (String imagePath in imagePaths) {
        try {
          final imageFile = File(imagePath);
          if (await imageFile.exists()) {
            await imageFile.delete();
            _exportLogs.add('Cleaned up image file on exception: $imagePath');
          }
        } catch (cleanupError) {
          _exportLogs.add(
              'Failed to clean up image file on exception: $imagePath - $cleanupError');
        }
      }

      return false;
    }

    // If we reach here, no text tracks were processed

    return true;
  }

  static Future<void> _trimAudio(
    String inputPath,
    String outputPath,
    double startTime,
    double endTime,
  ) async {
    final duration = endTime - startTime;
    final command =
        '-i $inputPath -ss $startTime -t $duration -c copy $outputPath';
    await FFmpegKit.execute(command);
  }

  static Future<void> _trimVideo(
    String inputPath,
    String outputPath,
    double startTime,
    double endTime,
  ) async {
    final duration = endTime - startTime;
    final command =
        '-i $inputPath -ss $startTime -t $duration -c copy $outputPath';
    await FFmpegKit.execute(command);
  }

  static Future<void> _mixAudio(
    String videoPath,
    String audioPath,
    String outputPath,
    double videoVolume,
    double audioVolume,
  ) async {
    final command =
        '-i $videoPath -i $audioPath -filter_complex "[0:a]volume=$videoVolume[a1];[1:a]volume=$audioVolume[a2];[a1][a2]amix=inputs=2:duration=first[a]" -map 0:v -map "[a]" -c:v copy $outputPath';
    await FFmpegKit.execute(command);
  }

  static Future<void> _adjustVolume(
    String inputPath,
    String outputPath,
    double volume,
  ) async {
    final command =
        '-i $inputPath -filter:a "volume=$volume" -c:v copy $outputPath';
    await FFmpegKit.execute(command);
  }

  static Future<void> _addTextOverlays(
    String videoPath,
    List<TextOverlay> overlays,
  ) async {
    String drawTextFilters = overlays.map((overlay) {
      return 'drawtext=text=${overlay.text}:x=${overlay.position.dx}:y=${overlay.position.dy}:'
          'fontsize=${overlay.style.fontSize}:fontcolor=0x${(overlay.color.r * 255).round().toRadixString(16).padLeft(2, '0')}${(overlay.color.g * 255).round().toRadixString(16).padLeft(2, '0')}${(overlay.color.b * 255).round().toRadixString(16).padLeft(2, '0')}';
    }).join(',');

    final command =
        '-i $videoPath -vf "$drawTextFilters" -c:a copy ${videoPath}_with_text.mp4';
    await FFmpegKit.execute(command);
  }

  /// ✅ FIXED: Calculate rotation-aware gaps using the EXACT same logic as the preview system
  /// The preview system constrains rotated video to fit within the current video display area
  /// This ensures export coordinates match preview coordinates exactly
  static Map<String, double> _calculateRotationAwareGaps({
    required double videoWidth,
    required double videoHeight,
    required double containerWidth,
    required double containerHeight,
    required int? rotation,
  }) {
    _exportLogs.add('=== Rotation-Aware Gap Calculation (Export) ===');
    _exportLogs.add('Video dimensions: ${videoWidth}x${videoHeight}');
    _exportLogs
        .add('Container dimensions: ${containerWidth}x${containerHeight}');
    _exportLogs.add('Rotation: ${rotation ?? 0}°');

    // ✅ FIXED: First calculate the CURRENT video display area (same as preview system)
    // This gives us the base area that the rotated video must fit within
    final currentVideoArea = _calculateCurrentVideoDisplayArea(
      videoWidth: videoWidth,
      videoHeight: videoHeight,
      containerWidth: containerWidth,
      containerHeight: containerHeight,
    );

    _exportLogs.add(
        'Current video display area: ${currentVideoArea.width}x${currentVideoArea.height}');
    _exportLogs.add(
        'Current video area position: left=${currentVideoArea.left}, top=${currentVideoArea.top}');

    // ✅ FIXED: Now fit the rotated video within the CURRENT video display area (same as preview system)
    final rotatedVideoArea = _applyRotationToVideoArea(
      currentVideoArea: currentVideoArea,
      videoWidth: videoWidth,
      videoHeight: videoHeight,
      rotation: rotation,
    );

    _exportLogs.add(
        'Rotated video area: ${rotatedVideoArea.width}x${rotatedVideoArea.height}');
    _exportLogs.add(
        'Rotated video position: left=${rotatedVideoArea.left}, top=${rotatedVideoArea.top}');

    // ✅ FIXED: Extract gaps from the rotated video area (same as preview system)
    final gapLeft = rotatedVideoArea.left;
    final gapTop = rotatedVideoArea.top;
    final actualPreviewWidth = rotatedVideoArea.width;
    final actualPreviewHeight = rotatedVideoArea.height;

    _exportLogs.add('Final rotation-aware gaps: left=$gapLeft, top=$gapTop');
    _exportLogs.add(
        'Final rotation-aware preview area: ${actualPreviewWidth}x${actualPreviewHeight}');
    _exportLogs.add('=== End Rotation-Aware Gap Calculation (Export) ===');

    return {
      'actualPreviewWidth': actualPreviewWidth,
      'actualPreviewHeight': actualPreviewHeight,
      'gapLeft': gapLeft,
      'gapTop': gapTop,
    };
  }

  /// ✅ NEW: Calculate the current video display area (same logic as preview system)
  static Rect _calculateCurrentVideoDisplayArea({
    required double videoWidth,
    required double videoHeight,
    required double containerWidth,
    required double containerHeight,
  }) {
    // Use the EXACT same logic as CanvasCoordinateManager.calculateContainerFitting
    final videoAspectRatio = videoWidth / videoHeight;
    final containerAspectRatio = containerWidth / containerHeight;

    double actualPreviewWidth, actualPreviewHeight, gapLeft, gapTop;

    if (videoAspectRatio > containerAspectRatio) {
      // Video is wider - fit width, letterbox top/bottom
      actualPreviewWidth = containerWidth;
      actualPreviewHeight = containerWidth / videoAspectRatio;
      gapLeft = 0.0;
      gapTop = (containerHeight - actualPreviewHeight) / 2.0;
    } else {
      // Video is taller - fit height, letterbox left/right
      actualPreviewHeight = containerHeight;
      actualPreviewWidth = containerHeight * videoAspectRatio;
      gapLeft = (containerWidth - actualPreviewWidth) / 2.0;
      gapTop = 0.0;
    }

    return Rect.fromLTWH(
        gapLeft, gapTop, actualPreviewWidth, actualPreviewHeight);
  }

  /// ✅ NEW: Apply rotation to video area (same logic as preview system)
  static Rect _applyRotationToVideoArea({
    required Rect currentVideoArea,
    required double videoWidth,
    required double videoHeight,
    required int? rotation,
  }) {
    if (rotation == null || rotation == 0) {
      return currentVideoArea;
    }

    // Calculate the effective video dimensions after rotation
    final isRotated = rotation == 90 || rotation == 270;
    double effectiveVideoWidth, effectiveVideoHeight;

    if (isRotated) {
      // Swap dimensions for rotated video (90° and 270°) - same as preview system
      effectiveVideoWidth = videoHeight;
      effectiveVideoHeight = videoWidth;
    } else {
      // Keep original dimensions for 0° and 180°
      effectiveVideoWidth = videoWidth;
      effectiveVideoHeight = videoHeight;
    }

    // ✅ FIXED: Fit the rotated video within the CURRENT video display area (same as preview system)
    final currentAspectRatio = currentVideoArea.width / currentVideoArea.height;
    final rotatedAspectRatio = effectiveVideoWidth / effectiveVideoHeight;

    double rotatedWidth, rotatedHeight, rotatedLeft, rotatedTop;

    if (rotatedAspectRatio > currentAspectRatio) {
      // Rotated video is wider - fit width, center height within current area
      rotatedWidth = currentVideoArea.width;
      rotatedHeight = currentVideoArea.width / rotatedAspectRatio;
      rotatedTop = currentVideoArea.top +
          (currentVideoArea.height - rotatedHeight) / 2.0;
      rotatedLeft = currentVideoArea.left;
    } else {
      // Rotated video is taller - fit height, center width within current area
      rotatedHeight = currentVideoArea.height;
      rotatedWidth = currentVideoArea.height * rotatedAspectRatio;
      rotatedLeft =
          currentVideoArea.left + (currentVideoArea.width - rotatedWidth) / 2.0;
      rotatedTop = currentVideoArea.top;
    }

    return Rect.fromLTWH(rotatedLeft, rotatedTop, rotatedWidth, rotatedHeight);
  }

  static Future<void> _createTextImage({
    required String text,
    required String fontFamily,
    required double fontSize,
    required Color color,
    required double rotation,
    required double maxWidth,
    required double maxHeight,
    required String outputPath,
  }) async {
    // Create text style with consistent line height
    final textStyle = TextStyle(
      fontSize: fontSize,
      fontFamily: fontFamily,
      color: color,
      height: 1.0, // Use consistent line height to match preview
    );

    // Wrap text if needed
    List<String> lines = TextAutoWrapHelper.wrapTextToFit(
      text,
      maxWidth,
      maxHeight,
      textStyle,
    );

    // Create text painter with consistent line height
    final textPainter = TextPainter(
      text: TextSpan(
        text: lines.join('\n'),
        style: textStyle,
      ),
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.left,
      textHeightBehavior: TextHeightBehavior(
        leadingDistribution: TextLeadingDistribution.even,
      ),
    );

    textPainter.layout(maxWidth: maxWidth);

    // Calculate canvas size (with padding for rotation)
    double textWidth = textPainter.width;
    double textHeight = textPainter.height;

    // If rotated, calculate bounding box from center reference point
    double canvasWidth, canvasHeight;
    if (rotation != 0) {
      final angleRad = rotation * math.pi / 180;
      final cos = math.cos(angleRad).abs();
      final sin = math.sin(angleRad).abs();

      // Calculate the bounding box dimensions when rotating around the center
      // This ensures the rotated text fits within the canvas when rotated around its center
      canvasWidth = textWidth * cos + textHeight * sin;
      canvasHeight = textWidth * sin + textHeight * cos;

      // Add extra padding to ensure rotated text doesn't get clipped
      // This accounts for the fact that rotation around center can extend beyond original bounds
      canvasWidth += 8; // Extra padding for center-based rotation
      canvasHeight += 8;
    } else {
      canvasWidth = textWidth;
      canvasHeight = textHeight;
    }

    // Add small padding
    canvasWidth += 4;
    canvasHeight += 4;

    // Create picture recorder
    final recorder = ui.PictureRecorder();
    final canvas = ui.Canvas(recorder);

    // Apply rotation if needed
    if (rotation != 0) {
      // IMPROVED APPROACH: Paint text at center, rotate around center, then compensate for top-left positioning

      // Calculate the center of the canvas
      final centerX = canvasWidth / 2;
      final centerY = canvasHeight / 2;

      // Calculate the center of the original text (before rotation)
      final textCenterX = textWidth / 2;
      final textCenterY = textHeight / 2;

      // Apply rotation transformation around the center of the canvas
      canvas.translate(centerX, centerY);
      canvas.rotate(rotation * math.pi / 180);
      canvas.translate(-centerX, -centerY);

      // Paint text at the center of the canvas
      // This ensures the text is centered within the rotated bounding box
      final textOffset = Offset(
        centerX - textCenterX,
        centerY - textCenterY,
      );
      textPainter.paint(canvas, textOffset);
    } else {
      // For non-rotated text, paint at top-left with small padding
      canvas.translate(2, 2);
      textPainter.paint(canvas, Offset.zero);
    }

    // Convert to image
    final picture = recorder.endRecording();
    final image = await picture.toImage(
      canvasWidth.ceil(),
      canvasHeight.ceil(),
    );

    // Convert to PNG
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    final pngBytes = byteData!.buffer.asUint8List();

    // Save to file
    final file = File(outputPath);
    await file.writeAsBytes(pngBytes);

    // Dispose all UI objects to free memory
    try {
      // Dispose TextPainter
      textPainter.dispose();

      // Dispose Picture
      picture.dispose();

      // Dispose ui.Image
      image.dispose();

      _exportLogs
          .add('Successfully disposed UI objects for text image: $outputPath');
    } catch (e) {
      _exportLogs.add('Warning: Failed to dispose some UI objects: $e');
    }
  }

  /// ✅ NEW: Validate coordinate transformation to ensure robustness
  static void _validateCoordinateTransformation({
    required int trackIndex,
    required Offset originalPosition,
    required Offset adjustedPosition, // ✅ NEW: Include adjusted position
    required Offset
        coordinateSystemOffset, // ✅ NEW: Include coordinate system offset
    required Offset videoPosition,
    required Offset cropSpace,
    required Offset croppedVideoCoords,
    required Offset finalExportPosition,
    required int? rotation,
    required Size exportDimensions,
  }) {
    // Log validation details
    debugPrint(
        'Track $trackIndex: [VALIDATION] Coordinate transformation validation:');
    debugPrint(
        'Track $trackIndex: [VALIDATION] - Original preview position: (${originalPosition.dx.toStringAsFixed(2)}, ${originalPosition.dy.toStringAsFixed(2)})');
    debugPrint(
        'Track $trackIndex: [VALIDATION] - Coordinate system offset: (${coordinateSystemOffset.dx.toStringAsFixed(2)}, ${coordinateSystemOffset.dy.toStringAsFixed(2)})');
    debugPrint(
        'Track $trackIndex: [VALIDATION] - Adjusted position: (${adjustedPosition.dx.toStringAsFixed(2)}, ${adjustedPosition.dy.toStringAsFixed(2)})');
    debugPrint(
        'Track $trackIndex: [VALIDATION] - Video coordinates: (${videoPosition.dx.toStringAsFixed(2)}, ${videoPosition.dy.toStringAsFixed(2)})');
    debugPrint(
        'Track $trackIndex: [VALIDATION] - Crop space: (${cropSpace.dx.toStringAsFixed(2)}, ${cropSpace.dy.toStringAsFixed(2)})');
    debugPrint(
        'Track $trackIndex: [VALIDATION] - Cropped video coords: (${croppedVideoCoords.dx.toStringAsFixed(3)}, ${croppedVideoCoords.dy.toStringAsFixed(3)})');
    debugPrint(
        'Track $trackIndex: [VALIDATION] - Final export position: (${finalExportPosition.dx.toStringAsFixed(2)}, ${finalExportPosition.dy.toStringAsFixed(2)})');
    debugPrint(
        'Track $trackIndex: [VALIDATION] - Export dimensions: ${exportDimensions.width}x${exportDimensions.height}');
    debugPrint('Track $trackIndex: [VALIDATION] - Rotation: ${rotation ?? 0}°');

    // Validate that final export position is within reasonable bounds
    final isWithinBounds = finalExportPosition.dx >= -50 &&
        finalExportPosition.dx <= exportDimensions.width + 50 &&
        finalExportPosition.dy >= -50 &&
        finalExportPosition.dy <= exportDimensions.height + 50;

    if (!isWithinBounds) {
      debugPrint(
          'Track $trackIndex: [VALIDATION] WARNING: Final export position outside reasonable bounds!');
      debugPrint(
          'Track $trackIndex: [VALIDATION] WARNING: Final export position outside reasonable bounds!');
    } else {
      debugPrint(
          'Track $trackIndex: [VALIDATION] SUCCESS: All coordinates are within reasonable bounds');
    }

    // Validate that cropped video coordinates are within [0, 1] range
    final isCroppedCoordsValid = croppedVideoCoords.dx >= 0 &&
        croppedVideoCoords.dx <= 1 &&
        croppedVideoCoords.dy >= 0 &&
        croppedVideoCoords.dy <= 1;

    if (!isCroppedCoordsValid) {
      _exportLogs.add(
          'Track $trackIndex: [VALIDATION] WARNING: Cropped video coordinates outside [0, 1] range!');
      debugPrint(
          'Track $trackIndex: [VALIDATION] WARNING: Cropped video coordinates outside [0, 1] range!');
    } else {
      debugPrint(
          'Track $trackIndex: [VALIDATION] SUCCESS: Cropped video coordinates are within valid range');
    }
  }
}
