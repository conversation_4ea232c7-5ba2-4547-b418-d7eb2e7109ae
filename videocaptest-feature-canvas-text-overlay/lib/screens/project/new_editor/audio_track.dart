import 'dart:io';

import 'package:ai_video_creator_editor/components/track_options.dart';
import 'package:ai_video_creator_editor/enums/track_type.dart';
import 'package:ai_video_creator_editor/screens/project/models/audio_track_model.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/custom_trim_slider.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/video_editor_provider.dart';
import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:flutter/material.dart';
import 'package:path/path.dart' as p;
import 'package:provider/provider.dart';
import 'package:video_player/video_player.dart';

class AudioTrack extends StatefulWidget {
  const AudioTrack({
    super.key,
    required this.audioTrack,
    required this.index,
    required this.isSelected,
    required this.timelineWidth,
    required this.timelineDuration,
    required this.selectedTrackBorderColor,
  });

  final AudioTrackModel audioTrack;
  final int index;
  final bool isSelected;
  final double timelineWidth;
  final double timelineDuration;
  final Color selectedTrackBorderColor;

  @override
  State<AudioTrack> createState() => _AudioTrackState();
}

class _AudioTrackState extends State<AudioTrack>
    with AutomaticKeepAliveClientMixin {
  final PlayerController _playerController = PlayerController();
  VideoPlayerController? _videoPlayerController;
  late ValueNotifier<bool> _isAudioPrepared;
  late String fileName;

  TrimBoundaries _boundary = TrimBoundaries.none;
  Rect _trimRect = Rect.zero;
  double _trimStart = 0.0;
  double _trimEnd = 0.0;

  OverlayEntry? _overlayEntry;
  final GlobalKey _trackKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _trimStart = widget.audioTrack.trimStartTime;
    _trimEnd = widget.audioTrack.trimEndTime;
    _isAudioPrepared = ValueNotifier(false);
    _videoPlayerController =
        context.read<VideoEditorProvider>().videoEditorController?.video;
    _prepareAudio(widget.audioTrack.audioFile);
  }

  Future<void> _prepareAudio(File? file) async {
    if (file == null) return;
    fileName = p.basename(file.path);
    await _playerController.preparePlayer(path: file.path);
    _isAudioPrepared.value = true;
    _videoPlayerController?.addListener(_syncAudioWithVideo);
  }

  void _syncAudioWithVideo() async {
    if (_videoPlayerController == null || !_isAudioPrepared.value) return;

    final videoPosition = _videoPlayerController!.value.position.inMilliseconds;
    final startTime = (widget.audioTrack.trimStartTime * 1000).toInt();
    final endTime = (widget.audioTrack.trimEndTime * 1000).toInt();
    final isVideoPlaying = _videoPlayerController!.value.isPlaying;
    final isAudioPlaying = _playerController.playerState.isPlaying;

    if (videoPosition >= startTime && videoPosition < endTime) {
      if (isVideoPlaying && !isAudioPlaying) {
        await _playerController.seekTo(videoPosition - startTime);
        await _playerController.startPlayer();
        await _playerController.setFinishMode(finishMode: FinishMode.loop);
      } else if (!isVideoPlaying && isAudioPlaying) {
        await _playerController.pausePlayer();
      }
    } else if (isAudioPlaying) {
      await _playerController.pausePlayer();
    }
  }

  Rect _getTrimRect() {
    double left = (_trimStart / widget.timelineDuration) * widget.timelineWidth;
    double right = (_trimEnd / widget.timelineDuration) * widget.timelineWidth;

    left = left.isNaN ? 0 : left;
    right = right.isNaN ? 0 : right;

    return Rect.fromLTRB(left, 0, right, 30);
  }

  void _onPanUpdate(DragUpdateDetails details) {
    if (_boundary == TrimBoundaries.none || !widget.isSelected) return;

    final audioTracks = context.read<VideoEditorProvider>().audioTracks;
    final isFirstTrack = widget.index == 0;
    final isLastTrack = widget.index == audioTracks.length - 1;

    final double lowerLimit = (audioTracks.length == 1 || isFirstTrack)
        ? 0
        : audioTracks[widget.index - 1].trimEndTime;

    final double upperLimit = (audioTracks.length == 1 || isLastTrack)
        ? widget.timelineDuration.toDouble()
        : audioTracks[widget.index + 1].trimStartTime;

    final delta =
        details.delta.dx / widget.timelineWidth * widget.timelineDuration;
    const double minTrimSize = 1;

    void updateTrim(double newStart, double newEnd) {
      _trimStart = newStart;
      _trimEnd = newEnd;
      context
          .read<VideoEditorProvider>()
          .updateAudioTrack(widget.index, _trimStart, _trimEnd);
    }

    switch (_boundary) {
      case TrimBoundaries.start:
        updateTrim(
            (_trimStart + delta).clamp(lowerLimit, _trimEnd - minTrimSize),
            _trimEnd);
        break;

      case TrimBoundaries.end:
        updateTrim(_trimStart,
            (_trimEnd + delta).clamp(_trimStart + minTrimSize, upperLimit));
        break;

      case TrimBoundaries.inside:
        final length = _trimEnd - _trimStart;
        var newStart =
            (_trimStart + delta).clamp(lowerLimit, upperLimit - length);
        updateTrim(newStart, newStart + length);
        break;

      case TrimBoundaries.none:
        break;
    }
  }

  @override
  void dispose() {
    _playerController.stopPlayer();
    _playerController.dispose();
    _isAudioPrepared.dispose();
    _videoPlayerController?.removeListener(_syncAudioWithVideo);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    _trimRect = _getTrimRect();
    final provider = context.watch<VideoEditorProvider>();
    final isMuted = provider.isAudioMuted(widget.audioTrack.id);
    // Set player volume based on mute state
    _playerController.setVolume(isMuted ? 0.0 : 1.0);
    return SizedBox(
      width: widget.timelineWidth,
      // child: ClipRect(
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Positioned.fromRect(
            rect: _trimRect,
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: widget.selectedTrackBorderColor,
                  width: 2,
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      key: _trackKey,
                      onTap: () => _showOverlay(context),
                      onHorizontalDragUpdate: (details) {
                        _boundary = TrimBoundaries.inside;
                        _onPanUpdate(details);
                      },
                      child: ValueListenableBuilder<bool>(
                        valueListenable: _isAudioPrepared,
                        builder: (context, isAudioPrepared, _) {
                          if (!isAudioPrepared) {
                            return const SizedBox.shrink();
                          }
                          return Container(
                            constraints: BoxConstraints(
                              maxWidth: _trimRect.width,
                              minWidth: 0,
                            ),
                            padding: const EdgeInsets.symmetric(horizontal: 12),
                            alignment: Alignment.centerLeft,
                            child: ClipRect(
                              child: Text(
                                "${(_trimEnd - _trimStart).toStringAsFixed(1)} | ${fileName}",
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                softWrap: false,
                                style: const TextStyle(fontSize: 12),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  // Mute/unmute button
                  // IconButton(
                  //   icon: Icon(
                  //     isMuted ? Icons.volume_off : Icons.volume_up,
                  //     color: isMuted ? Colors.red : Colors.white,
                  //   ),
                  //   tooltip: isMuted ? 'Unmute Audio' : 'Mute Audio',
                  //   onPressed: () {
                  //     provider.toggleAudioMute(widget.audioTrack.id);
                  //   },
                  // ),
                ],
              ),
            ),
          ),

          // Start handle
          if (widget.isSelected)
            Positioned(
              left: _trimRect.left - 10,
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onHorizontalDragUpdate: (details) {
                  _boundary = TrimBoundaries.start;
                  _onPanUpdate(details);
                },
                child: Container(
                  width: 20,
                  height: 30,
                  decoration: BoxDecoration(
                    color: widget.selectedTrackBorderColor,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(4),
                      bottomLeft: Radius.circular(4),
                    ),
                  ),
                  child: Center(
                    child: Container(
                      width: 2,
                      height: 15,
                      decoration: BoxDecoration(
                        color: Colors.grey,
                        borderRadius: BorderRadius.all(
                          Radius.circular(double.maxFinite),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),

          // End handle
          if (widget.isSelected)
            Positioned(
              left: _trimRect.right - 10,
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onHorizontalDragUpdate: (details) {
                  _boundary = TrimBoundaries.end;
                  _onPanUpdate(details);
                },
                child: Container(
                  width: 20,
                  height: 30,
                  decoration: BoxDecoration(
                    color: widget.selectedTrackBorderColor,
                    borderRadius: BorderRadius.only(
                      topRight: Radius.circular(4),
                      bottomRight: Radius.circular(4),
                    ),
                  ),
                  child: Center(
                    child: Container(
                      width: 2,
                      height: 15,
                      decoration: BoxDecoration(
                        color: Colors.grey,
                        borderRadius: BorderRadius.all(
                          Radius.circular(double.maxFinite),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  void _showOverlay(BuildContext context) {
    final RenderBox renderBox =
        _trackKey.currentContext?.findRenderObject() as RenderBox;
    final Offset offset = renderBox.localToGlobal(Offset.zero);
    final provider = context.read<VideoEditorProvider>();
    final isMuted = provider.isAudioMuted(widget.audioTrack.id);

    _overlayEntry = OverlayEntry(
      builder: (context) => TrackOptions(
        offset: offset,
        trackType: TrackType.audio,
        onTap: _hideOverlay,
        onTrim: () {
          context.read<VideoEditorProvider>().setAudioTrackIndex(widget.index);
          _hideOverlay();
        },
        onDelete: () {
          context.read<VideoEditorProvider>().removeAudioTrack(widget.index);
          _hideOverlay();
        },
        onMute: () {
          provider.toggleAudioMute(widget.audioTrack.id);
          _hideOverlay();
        },
        isMuted: isMuted,
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hideOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  @override
  bool get wantKeepAlive => true;
}
