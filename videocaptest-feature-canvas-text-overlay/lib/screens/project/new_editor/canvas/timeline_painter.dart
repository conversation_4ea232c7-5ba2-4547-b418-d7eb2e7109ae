import 'package:flutter/material.dart';
import 'package:ai_video_creator_editor/screens/project/models/video_track_model.dart';
import 'package:ai_video_creator_editor/screens/project/models/audio_track_model.dart';
import 'package:ai_video_creator_editor/screens/project/models/text_track_model.dart';
import 'package:ai_video_creator_editor/screens/project/models/overlay_video_track_model.dart';
import 'dart:math' as math;

/// Comprehensive Timeline UI using CustomPainter for video editor
/// Renders video, audio, text, and overlay tracks with proper scaling and positioning
class TimelinePainter extends CustomPainter {
  final List<VideoTrackModel> videoTracks;
  final List<AudioTrackModel> audioTracks;
  final List<TextTrackModel> textTracks;
  final List<OverlayVideoTrackModel> overlayTracks;
  final double currentTime;
  final double totalDuration;
  final double pixelsPerSecond;
  final double trackHeight;
  final String? selectedClipId;
  final Color backgroundColor;
  final Color trackColor;
  final Color selectedTrackColor;
  final Color playheadColor;
  final Color waveformColor;
  final bool showWaveform;

  TimelinePainter({
    required this.videoTracks,
    required this.audioTracks,
    required this.textTracks,
    required this.overlayTracks,
    required this.currentTime,
    required this.totalDuration,
    required this.pixelsPerSecond,
    this.trackHeight = 60.0,
    this.selectedClipId,
    this.backgroundColor = const Color(0xFF1E1E1E),
    this.trackColor = const Color(0xFF3A3A3A),
    this.selectedTrackColor = const Color(0xFF4A90E2),
    this.playheadColor = const Color(0xFFFF6B6B),
    this.waveformColor = const Color(0xFF66BB6A),
    this.showWaveform = true,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Draw background
    _drawBackground(canvas, size);
    
    // Calculate track positions
    double currentY = 0;
    
    // Draw video tracks
    for (int i = 0; i < videoTracks.length; i++) {
      _drawVideoTrack(canvas, videoTracks[i], currentY, size.width);
      currentY += trackHeight + 4; // 4px spacing between tracks
    }
    
    // Draw audio tracks
    for (int i = 0; i < audioTracks.length; i++) {
      _drawAudioTrack(canvas, audioTracks[i], currentY, size.width);
      currentY += trackHeight + 4;
    }
    
    // Draw text tracks
    for (int i = 0; i < textTracks.length; i++) {
      _drawTextTrack(canvas, textTracks[i], currentY, size.width);
      currentY += trackHeight + 4;
    }
    
    // Draw overlay tracks
    for (int i = 0; i < overlayTracks.length; i++) {
      _drawOverlayTrack(canvas, overlayTracks[i], currentY, size.width);
      currentY += trackHeight + 4;
    }
    
    // Draw playhead
    _drawPlayhead(canvas, size);
    
    // Draw time markers
    _drawTimeMarkers(canvas, size);
  }

  void _drawBackground(Canvas canvas, Size size) {
    final paint = Paint()..color = backgroundColor;
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), paint);
  }

  void _drawVideoTrack(Canvas canvas, VideoTrackModel track, double y, double width) {
    final startX = track.startTime * pixelsPerSecond;
    final trackWidth = (track.endTime - track.startTime) * pixelsPerSecond;
    final isSelected = selectedClipId == track.id;
    
    // Draw track background
    final paint = Paint()
      ..color = isSelected ? selectedTrackColor : trackColor
      ..style = PaintingStyle.fill;
    
    final rect = Rect.fromLTWH(startX, y, trackWidth, trackHeight);
    canvas.drawRRect(
      RRect.fromRectAndRadius(rect, const Radius.circular(4)),
      paint,
    );
    
    // Draw track border
    final borderPaint = Paint()
      ..color = isSelected ? selectedTrackColor.withOpacity(0.8) : Colors.grey.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = isSelected ? 2 : 1;
    
    canvas.drawRRect(
      RRect.fromRectAndRadius(rect, const Radius.circular(4)),
      borderPaint,
    );
    
    // Draw track label
    _drawTrackLabel(canvas, 'Video ${videoTracks.indexOf(track) + 1}', rect, Colors.white);
    
    // Draw trim indicators if track is trimmed
    if (track.videoTrimStart > 0 || track.videoTrimEnd < track.originalDuration) {
      _drawTrimIndicators(canvas, rect, track.videoTrimStart, track.videoTrimEnd, track.originalDuration);
    }
  }

  void _drawAudioTrack(Canvas canvas, AudioTrackModel track, double y, double width) {
    final startX = track.trimStartTime * pixelsPerSecond;
    final trackWidth = (track.trimEndTime - track.trimStartTime) * pixelsPerSecond;
    
    // Draw track background
    final paint = Paint()
      ..color = const Color(0xFF2E7D32) // Green for audio
      ..style = PaintingStyle.fill;
    
    final rect = Rect.fromLTWH(startX, y, trackWidth, trackHeight);
    canvas.drawRRect(
      RRect.fromRectAndRadius(rect, const Radius.circular(4)),
      paint,
    );
    
    // Draw waveform if enabled
    if (showWaveform) {
      _drawWaveform(canvas, rect);
    }
    
    // Draw track label
    _drawTrackLabel(canvas, 'Audio ${audioTracks.indexOf(track) + 1}', rect, Colors.white);
  }

  void _drawTextTrack(Canvas canvas, TextTrackModel track, double y, double width) {
    final startX = track.trimStartTime * pixelsPerSecond;
    final trackWidth = (track.trimEndTime - track.trimStartTime) * pixelsPerSecond;
    
    // Draw track background
    final paint = Paint()
      ..color = const Color(0xFF7B1FA2) // Purple for text
      ..style = PaintingStyle.fill;
    
    final rect = Rect.fromLTWH(startX, y, trackWidth, trackHeight);
    canvas.drawRRect(
      RRect.fromRectAndRadius(rect, const Radius.circular(4)),
      paint,
    );
    
    // Draw track label with text preview
    final previewText = track.text.length > 20 ? '${track.text.substring(0, 20)}...' : track.text;
    _drawTrackLabel(canvas, previewText, rect, Colors.white);
  }

  void _drawOverlayTrack(Canvas canvas, OverlayVideoTrackModel track, double y, double width) {
    final startX = track.trimStartTime * pixelsPerSecond;
    final trackWidth = (track.trimEndTime - track.trimStartTime) * pixelsPerSecond;
    
    // Draw track background
    final paint = Paint()
      ..color = const Color(0xFFE65100) // Orange for overlay
      ..style = PaintingStyle.fill;
    
    final rect = Rect.fromLTWH(startX, y, trackWidth, trackHeight);
    canvas.drawRRect(
      RRect.fromRectAndRadius(rect, const Radius.circular(4)),
      paint,
    );
    
    // Draw opacity indicator
    final opacityPaint = Paint()
      ..color = Colors.white.withOpacity(track.opacity)
      ..style = PaintingStyle.fill;
    
    final opacityRect = Rect.fromLTWH(startX + 2, y + 2, trackWidth - 4, 4);
    canvas.drawRect(opacityRect, opacityPaint);
    
    // Draw track label
    _drawTrackLabel(canvas, 'Overlay ${overlayTracks.indexOf(track) + 1}', rect, Colors.white);
  }

  void _drawPlayhead(Canvas canvas, Size size) {
    final playheadX = currentTime * pixelsPerSecond;
    final paint = Paint()
      ..color = playheadColor
      ..strokeWidth = 2;
    
    // Draw playhead line
    canvas.drawLine(
      Offset(playheadX, 0),
      Offset(playheadX, size.height),
      paint,
    );
    
    // Draw playhead handle
    final handlePaint = Paint()
      ..color = playheadColor
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(Offset(playheadX, 10), 6, handlePaint);
  }

  void _drawTimeMarkers(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey.withOpacity(0.5)
      ..strokeWidth = 1;
    
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );
    
    // Draw time markers every second
    for (double time = 0; time <= totalDuration; time += 1.0) {
      final x = time * pixelsPerSecond;
      
      // Draw marker line
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, 20),
        paint,
      );
      
      // Draw time label
      final timeText = '${time.toInt()}s';
      textPainter.text = TextSpan(
        text: timeText,
        style: const TextStyle(
          color: Colors.grey,
          fontSize: 10,
        ),
      );
      textPainter.layout();
      textPainter.paint(canvas, Offset(x - textPainter.width / 2, 22));
    }
  }

  void _drawTrackLabel(Canvas canvas, String label, Rect rect, Color textColor) {
    final textPainter = TextPainter(
      text: TextSpan(
        text: label,
        style: TextStyle(
          color: textColor,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout(maxWidth: rect.width - 8);
    
    // Center text vertically in the track
    final textY = rect.top + (rect.height - textPainter.height) / 2;
    textPainter.paint(canvas, Offset(rect.left + 4, textY));
  }

  void _drawWaveform(Canvas canvas, Rect rect) {
    final paint = Paint()
      ..color = waveformColor.withOpacity(0.6)
      ..strokeWidth = 1;
    
    // Simple waveform visualization (placeholder)
    final centerY = rect.top + rect.height / 2;
    final waveformPoints = <Offset>[];
    
    for (double x = rect.left; x < rect.right; x += 2) {
      final amplitude = math.sin((x - rect.left) * 0.1) * (rect.height * 0.3);
      waveformPoints.add(Offset(x, centerY + amplitude));
    }
    
    for (int i = 0; i < waveformPoints.length - 1; i++) {
      canvas.drawLine(waveformPoints[i], waveformPoints[i + 1], paint);
    }
  }

  void _drawTrimIndicators(Canvas canvas, Rect rect, double trimStart, double trimEnd, double totalDuration) {
    final paint = Paint()
      ..color = Colors.yellow.withOpacity(0.7)
      ..strokeWidth = 2;
    
    // Draw trim start indicator
    if (trimStart > 0) {
      canvas.drawLine(
        Offset(rect.left, rect.top),
        Offset(rect.left, rect.bottom),
        paint,
      );
    }
    
    // Draw trim end indicator
    if (trimEnd < totalDuration) {
      canvas.drawLine(
        Offset(rect.right, rect.top),
        Offset(rect.right, rect.bottom),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(TimelinePainter oldDelegate) {
    return oldDelegate.currentTime != currentTime ||
        oldDelegate.videoTracks != videoTracks ||
        oldDelegate.audioTracks != audioTracks ||
        oldDelegate.textTracks != textTracks ||
        oldDelegate.overlayTracks != overlayTracks ||
        oldDelegate.selectedClipId != selectedClipId ||
        oldDelegate.totalDuration != totalDuration ||
        oldDelegate.pixelsPerSecond != pixelsPerSecond;
  }
}
