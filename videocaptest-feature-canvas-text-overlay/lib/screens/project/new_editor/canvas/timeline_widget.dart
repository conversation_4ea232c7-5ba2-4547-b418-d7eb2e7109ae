import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/video_editor_provider.dart';
import 'timeline_painter.dart';
import 'timeline_interaction_handler.dart';
import 'dart:math' as math;

/// Comprehensive Timeline Widget with Canvas-based rendering
/// Provides interactive timeline with drag, drop, resize, and scrubbing functionality
class TimelineWidget extends StatefulWidget {
  final double height;
  final double pixelsPerSecond;
  final bool showWaveform;
  final VoidCallback? onTimelineChanged;

  const TimelineWidget({
    Key? key,
    this.height = 200.0,
    this.pixelsPerSecond = 50.0,
    this.showWaveform = true,
    this.onTimelineChanged,
  }) : super(key: key);

  @override
  State<TimelineWidget> createState() => _TimelineWidgetState();
}

class _TimelineWidgetState extends State<TimelineWidget> {
  late ScrollController _scrollController;
  late TimelineInteractionHandler _interactionHandler;
  
  // Drag state
  String? _draggedClipId;
  Offset? _dragStartPosition;
  double? _dragStartTime;
  bool _isDragging = false;
  bool _isResizing = false;
  String? _resizeClipId;
  bool _resizeFromStart = false;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _interactionHandler = TimelineInteractionHandler(
      pixelsPerSecond: widget.pixelsPerSecond,
      onClipMoved: _handleClipMoved,
      onClipResized: _handleClipResized,
      onTimelineSeek: _handleTimelineSeek,
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<VideoEditorProvider>(
      builder: (context, provider, child) {
        final totalWidth = provider.videoDuration * widget.pixelsPerSecond;
        
        return Container(
          height: widget.height,
          decoration: BoxDecoration(
            color: const Color(0xFF1E1E1E),
            border: Border.all(color: Colors.grey.withOpacity(0.3)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              // Timeline header with time markers
              _buildTimelineHeader(provider, totalWidth),
              
              // Main timeline area
              Expanded(
                child: SingleChildScrollView(
                  controller: _scrollController,
                  scrollDirection: Axis.horizontal,
                  child: GestureDetector(
                    onTapDown: _handleTapDown,
                    onPanStart: _handlePanStart,
                    onPanUpdate: _handlePanUpdate,
                    onPanEnd: _handlePanEnd,
                    child: CustomPaint(
                      size: Size(totalWidth, widget.height - 40), // 40px for header
                      painter: TimelinePainter(
                        videoTracks: provider.videoTracks,
                        audioTracks: provider.audioTracks,
                        textTracks: provider.textTracks,
                        overlayTracks: provider.overlayVideoTracks,
                        currentTime: provider.videoPosition,
                        totalDuration: provider.videoDuration,
                        pixelsPerSecond: widget.pixelsPerSecond,
                        selectedClipId: provider.selectedClipId,
                        showWaveform: widget.showWaveform,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTimelineHeader(VideoEditorProvider provider, double totalWidth) {
    return Container(
      height: 40,
      width: totalWidth,
      child: CustomPaint(
        painter: TimelineHeaderPainter(
          totalDuration: provider.videoDuration,
          pixelsPerSecond: widget.pixelsPerSecond,
          currentTime: provider.videoPosition,
        ),
      ),
    );
  }

  void _handleTapDown(TapDownDetails details) {
    final provider = Provider.of<VideoEditorProvider>(context, listen: false);
    final result = _interactionHandler.handleTapDown(
      details.localPosition,
      provider.videoTracks,
      provider.audioTracks,
      provider.textTracks,
      provider.overlayVideoTracks,
    );
    
    if (result.clipId != null) {
      provider.selectClip(result.clipId!);
    } else {
      // Seek to tapped position
      final time = details.localPosition.dx / widget.pixelsPerSecond;
      provider.seekTo(time);
    }
  }

  void _handlePanStart(DragStartDetails details) {
    final provider = Provider.of<VideoEditorProvider>(context, listen: false);
    final result = _interactionHandler.handlePanStart(
      details.localPosition,
      provider.videoTracks,
      provider.audioTracks,
      provider.textTracks,
      provider.overlayVideoTracks,
    );
    
    setState(() {
      _draggedClipId = result.clipId;
      _dragStartPosition = details.localPosition;
      _dragStartTime = result.startTime;
      _isDragging = result.isDragging;
      _isResizing = result.isResizing;
      _resizeClipId = result.resizeClipId;
      _resizeFromStart = result.resizeFromStart;
    });
  }

  void _handlePanUpdate(DragUpdateDetails details) {
    if (!_isDragging && !_isResizing) return;
    
    final provider = Provider.of<VideoEditorProvider>(context, listen: false);
    
    if (_isDragging && _draggedClipId != null) {
      final deltaTime = details.delta.dx / widget.pixelsPerSecond;
      _interactionHandler.handleClipDrag(
        _draggedClipId!,
        deltaTime,
        provider,
      );
    } else if (_isResizing && _resizeClipId != null) {
      final deltaTime = details.delta.dx / widget.pixelsPerSecond;
      _interactionHandler.handleClipResize(
        _resizeClipId!,
        deltaTime,
        _resizeFromStart,
        provider,
      );
    }
  }

  void _handlePanEnd(DragEndDetails details) {
    setState(() {
      _draggedClipId = null;
      _dragStartPosition = null;
      _dragStartTime = null;
      _isDragging = false;
      _isResizing = false;
      _resizeClipId = null;
      _resizeFromStart = false;
    });
    
    widget.onTimelineChanged?.call();
  }

  void _handleClipMoved(String clipId, double newStartTime) {
    final provider = Provider.of<VideoEditorProvider>(context, listen: false);
    // Update clip position in provider
    // This will be implemented based on the specific clip type
  }

  void _handleClipResized(String clipId, double newDuration, bool fromStart) {
    final provider = Provider.of<VideoEditorProvider>(context, listen: false);
    // Update clip duration in provider
    // This will be implemented based on the specific clip type
  }

  void _handleTimelineSeek(double time) {
    final provider = Provider.of<VideoEditorProvider>(context, listen: false);
    provider.seekTo(time);
  }
}

/// Timeline Header Painter for time markers and ruler
class TimelineHeaderPainter extends CustomPainter {
  final double totalDuration;
  final double pixelsPerSecond;
  final double currentTime;

  TimelineHeaderPainter({
    required this.totalDuration,
    required this.pixelsPerSecond,
    required this.currentTime,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Draw background
    final backgroundPaint = Paint()..color = const Color(0xFF2A2A2A);
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), backgroundPaint);
    
    // Draw time markers
    final markerPaint = Paint()
      ..color = Colors.grey.withOpacity(0.6)
      ..strokeWidth = 1;
    
    final textPainter = TextPainter(textDirection: TextDirection.ltr);
    
    // Major markers every 5 seconds
    for (double time = 0; time <= totalDuration; time += 5.0) {
      final x = time * pixelsPerSecond;
      
      // Draw major marker line
      canvas.drawLine(
        Offset(x, size.height - 15),
        Offset(x, size.height),
        markerPaint,
      );
      
      // Draw time label
      final minutes = (time / 60).floor();
      final seconds = (time % 60).floor();
      final timeText = '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
      
      textPainter.text = TextSpan(
        text: timeText,
        style: const TextStyle(
          color: Colors.grey,
          fontSize: 10,
          fontWeight: FontWeight.w400,
        ),
      );
      textPainter.layout();
      
      if (x + textPainter.width / 2 < size.width) {
        textPainter.paint(canvas, Offset(x - textPainter.width / 2, 5));
      }
    }
    
    // Minor markers every 1 second
    final minorMarkerPaint = Paint()
      ..color = Colors.grey.withOpacity(0.3)
      ..strokeWidth = 0.5;
    
    for (double time = 0; time <= totalDuration; time += 1.0) {
      if (time % 5.0 != 0) { // Skip major markers
        final x = time * pixelsPerSecond;
        canvas.drawLine(
          Offset(x, size.height - 8),
          Offset(x, size.height),
          minorMarkerPaint,
        );
      }
    }
    
    // Draw current time indicator
    final playheadX = currentTime * pixelsPerSecond;
    final playheadPaint = Paint()
      ..color = const Color(0xFFFF6B6B)
      ..strokeWidth = 2;
    
    canvas.drawLine(
      Offset(playheadX, 0),
      Offset(playheadX, size.height),
      playheadPaint,
    );
  }

  @override
  bool shouldRepaint(TimelineHeaderPainter oldDelegate) {
    return oldDelegate.currentTime != currentTime ||
        oldDelegate.totalDuration != totalDuration ||
        oldDelegate.pixelsPerSecond != pixelsPerSecond;
  }
}
