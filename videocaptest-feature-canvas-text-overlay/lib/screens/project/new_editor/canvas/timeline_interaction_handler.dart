import 'package:flutter/material.dart';
import 'package:ai_video_creator_editor/screens/project/models/video_track_model.dart';
import 'package:ai_video_creator_editor/screens/project/models/audio_track_model.dart';
import 'package:ai_video_creator_editor/screens/project/models/text_track_model.dart';
import 'package:ai_video_creator_editor/screens/project/models/overlay_video_track_model.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/video_editor_provider.dart';

/// Handles user interactions on the timeline
/// Manages clip selection, dragging, resizing, and timeline scrubbing
class TimelineInteractionHandler {
  final double pixelsPerSecond;
  final Function(String clipId, double newStartTime)? onClipMoved;
  final Function(String clipId, double newDuration, bool fromStart)?
      onClipResized;
  final Function(double time)? onTimelineSeek;

  // Interaction constants
  static const double resizeHandleWidth = 8.0;
  static const double trackHeight = 60.0;
  static const double trackSpacing = 4.0;

  TimelineInteractionHandler({
    required this.pixelsPerSecond,
    this.onClipMoved,
    this.onClipResized,
    this.onTimelineSeek,
  });

  /// Handle tap down events on timeline
  TimelineInteractionResult handleTapDown(
    Offset position,
    List<VideoTrackModel> videoTracks,
    List<AudioTrackModel> audioTracks,
    List<TextTrackModel> textTracks,
    List<OverlayVideoTrackModel> overlayTracks,
  ) {
    final hitResult = _hitTestClips(
        position, videoTracks, audioTracks, textTracks, overlayTracks);

    if (hitResult.clipId != null) {
      return TimelineInteractionResult(
        clipId: hitResult.clipId,
        trackType: hitResult.trackType,
        startTime: hitResult.startTime,
      );
    }

    // No clip hit, return timeline seek position
    final time = position.dx / pixelsPerSecond;
    return TimelineInteractionResult(seekTime: time);
  }

  /// Handle pan start events for dragging and resizing
  TimelineInteractionResult handlePanStart(
    Offset position,
    List<VideoTrackModel> videoTracks,
    List<AudioTrackModel> audioTracks,
    List<TextTrackModel> textTracks,
    List<OverlayVideoTrackModel> overlayTracks,
  ) {
    final hitResult = _hitTestClips(
        position, videoTracks, audioTracks, textTracks, overlayTracks);

    if (hitResult.clipId == null) {
      return TimelineInteractionResult();
    }

    // Check if we're near the edges for resizing
    final clipRect = _getClipRect(
        hitResult.clipId!,
        hitResult.trackType!,
        hitResult.trackIndex!,
        videoTracks,
        audioTracks,
        textTracks,
        overlayTracks);

    if (clipRect != null) {
      final isNearLeftEdge = position.dx - clipRect.left < resizeHandleWidth;
      final isNearRightEdge = clipRect.right - position.dx < resizeHandleWidth;

      if (isNearLeftEdge || isNearRightEdge) {
        return TimelineInteractionResult(
          clipId: hitResult.clipId,
          trackType: hitResult.trackType,
          isResizing: true,
          resizeClipId: hitResult.clipId,
          resizeFromStart: isNearLeftEdge,
          startTime: hitResult.startTime,
        );
      }
    }

    // Default to dragging
    return TimelineInteractionResult(
      clipId: hitResult.clipId,
      trackType: hitResult.trackType,
      isDragging: true,
      startTime: hitResult.startTime,
    );
  }

  /// Handle clip dragging
  void handleClipDrag(
      String clipId, double deltaTime, VideoEditorProvider provider) {
    // Find the clip and update its position
    try {
      final videoTrack =
          provider.videoTracks.firstWhere((track) => track.id == clipId);
      final newStartTime =
          (videoTrack.startTime + deltaTime).clamp(0.0, provider.videoDuration);
      final duration = videoTrack.endTime - videoTrack.startTime;
      final newEndTime =
          (newStartTime + duration).clamp(duration, provider.videoDuration);

      // Update the track
      final updatedTrack = videoTrack.copyWith(
        startTime: newStartTime.toInt(),
        endTime: newEndTime.toInt(),
      );

      final index = provider.videoTracks.indexOf(videoTrack);
      provider.updateVideoTrack(index, updatedTrack);

      onClipMoved?.call(clipId, newStartTime);
    } catch (e) {
      print('Error handling clip drag: $e');
    }
  }

  /// Handle clip resizing
  void handleClipResize(String clipId, double deltaTime, bool fromStart,
      VideoEditorProvider provider) {
    try {
      final videoTrack =
          provider.videoTracks.firstWhere((track) => track.id == clipId);
      double newStartTime = videoTrack.startTime.toDouble();
      double newEndTime = videoTrack.endTime.toDouble();

      if (fromStart) {
        newStartTime = (videoTrack.startTime + deltaTime)
            .clamp(0.0, videoTrack.endTime - 0.1);
      } else {
        newEndTime = (videoTrack.endTime + deltaTime)
            .clamp(videoTrack.startTime + 0.1, provider.videoDuration);
      }

      final updatedTrack = videoTrack.copyWith(
        startTime: newStartTime.toInt(),
        endTime: newEndTime.toInt(),
      );

      final index = provider.videoTracks.indexOf(videoTrack);
      provider.updateVideoTrack(index, updatedTrack);

      onClipResized?.call(clipId, newEndTime - newStartTime, fromStart);
    } catch (e) {
      print('Error handling clip resize: $e');
    }
  }

  /// Hit test to find which clip was clicked
  _ClipHitResult _hitTestClips(
    Offset position,
    List<VideoTrackModel> videoTracks,
    List<AudioTrackModel> audioTracks,
    List<TextTrackModel> textTracks,
    List<OverlayVideoTrackModel> overlayTracks,
  ) {
    double currentY = 0;

    // Test video tracks
    for (int i = 0; i < videoTracks.length; i++) {
      final track = videoTracks[i];
      final rect = Rect.fromLTWH(
        track.startTime * pixelsPerSecond,
        currentY,
        (track.endTime - track.startTime) * pixelsPerSecond,
        trackHeight,
      );

      if (rect.contains(position)) {
        return _ClipHitResult(
          clipId: track.id,
          trackType: TrackType.video,
          trackIndex: i,
          startTime: track.startTime.toDouble(),
        );
      }
      currentY += trackHeight + trackSpacing;
    }

    // Test audio tracks
    for (int i = 0; i < audioTracks.length; i++) {
      final track = audioTracks[i];
      final rect = Rect.fromLTWH(
        track.trimStartTime * pixelsPerSecond,
        currentY,
        (track.trimEndTime - track.trimStartTime) * pixelsPerSecond,
        trackHeight,
      );

      if (rect.contains(position)) {
        return _ClipHitResult(
          clipId: track.id,
          trackType: TrackType.audio,
          trackIndex: i,
          startTime: track.trimStartTime,
        );
      }
      currentY += trackHeight + trackSpacing;
    }

    // Test text tracks
    for (int i = 0; i < textTracks.length; i++) {
      final track = textTracks[i];
      final rect = Rect.fromLTWH(
        track.trimStartTime * pixelsPerSecond,
        currentY,
        (track.trimEndTime - track.trimStartTime) * pixelsPerSecond,
        trackHeight,
      );

      if (rect.contains(position)) {
        return _ClipHitResult(
          clipId: track.id,
          trackType: TrackType.text,
          trackIndex: i,
          startTime: track.trimStartTime,
        );
      }
      currentY += trackHeight + trackSpacing;
    }

    // Test overlay tracks
    for (int i = 0; i < overlayTracks.length; i++) {
      final track = overlayTracks[i];
      final rect = Rect.fromLTWH(
        track.trimStartTime * pixelsPerSecond,
        currentY,
        (track.trimEndTime - track.trimStartTime) * pixelsPerSecond,
        trackHeight,
      );

      if (rect.contains(position)) {
        return _ClipHitResult(
          clipId: track.id,
          trackType: TrackType.overlay,
          trackIndex: i,
          startTime: track.trimStartTime,
        );
      }
      currentY += trackHeight + trackSpacing;
    }

    return _ClipHitResult();
  }

  /// Get the rectangle for a specific clip
  Rect? _getClipRect(
    String clipId,
    TrackType trackType,
    int trackIndex,
    List<VideoTrackModel> videoTracks,
    List<AudioTrackModel> audioTracks,
    List<TextTrackModel> textTracks,
    List<OverlayVideoTrackModel> overlayTracks,
  ) {
    double currentY = 0;

    // Calculate Y position based on track type and index
    if (trackType == TrackType.video) {
      currentY = trackIndex * (trackHeight + trackSpacing);
      final track = videoTracks[trackIndex];
      return Rect.fromLTWH(
        track.startTime * pixelsPerSecond,
        currentY,
        (track.endTime - track.startTime) * pixelsPerSecond,
        trackHeight,
      );
    }

    currentY = videoTracks.length * (trackHeight + trackSpacing);

    if (trackType == TrackType.audio) {
      currentY += trackIndex * (trackHeight + trackSpacing);
      final track = audioTracks[trackIndex];
      return Rect.fromLTWH(
        track.trimStartTime * pixelsPerSecond,
        currentY,
        (track.trimEndTime - track.trimStartTime) * pixelsPerSecond,
        trackHeight,
      );
    }

    // Add similar logic for text and overlay tracks...

    return null;
  }
}

/// Result of timeline interaction
class TimelineInteractionResult {
  final String? clipId;
  final TrackType? trackType;
  final bool isDragging;
  final bool isResizing;
  final String? resizeClipId;
  final bool resizeFromStart;
  final double? startTime;
  final double? seekTime;

  TimelineInteractionResult({
    this.clipId,
    this.trackType,
    this.isDragging = false,
    this.isResizing = false,
    this.resizeClipId,
    this.resizeFromStart = false,
    this.startTime,
    this.seekTime,
  });
}

/// Internal class for hit test results
class _ClipHitResult {
  final String? clipId;
  final TrackType? trackType;
  final int? trackIndex;
  final double? startTime;

  _ClipHitResult({
    this.clipId,
    this.trackType,
    this.trackIndex,
    this.startTime,
  });
}

/// Track types for timeline
enum TrackType {
  video,
  audio,
  text,
  overlay,
}
