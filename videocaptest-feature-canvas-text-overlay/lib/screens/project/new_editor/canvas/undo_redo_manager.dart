import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:ai_video_creator_editor/screens/project/models/video_track_model.dart';
import 'package:ai_video_creator_editor/screens/project/models/audio_track_model.dart';
import 'package:ai_video_creator_editor/screens/project/models/text_track_model.dart';
import 'package:ai_video_creator_editor/screens/project/models/overlay_video_track_model.dart';

/// Undo/Redo manager using command pattern
/// Manages project state history and provides undo/redo functionality
class UndoRedoManager extends ChangeNotifier {
  final List<ProjectCommand> _undoStack = [];
  final List<ProjectCommand> _redoStack = [];
  final int maxHistorySize;
  
  ProjectState? _currentState;
  
  UndoRedoManager({this.maxHistorySize = 50});

  /// Current project state
  ProjectState? get currentState => _currentState;
  
  /// Can undo
  bool get canUndo => _undoStack.isNotEmpty;
  
  /// Can redo
  bool get canRedo => _redoStack.isNotEmpty;
  
  /// Number of undo operations available
  int get undoCount => _undoStack.length;
  
  /// Number of redo operations available
  int get redoCount => _redoStack.length;

  /// Initialize with current state
  void initialize(ProjectState initialState) {
    _currentState = initialState;
    _undoStack.clear();
    _redoStack.clear();
    notifyListeners();
  }

  /// Execute a command and add to undo stack
  void executeCommand(ProjectCommand command) {
    if (_currentState == null) return;
    
    // Store current state before executing command
    final previousState = _currentState!.copy();
    
    // Execute the command
    _currentState = command.execute(_currentState!);
    
    // Add to undo stack
    _undoStack.add(command.withPreviousState(previousState));
    
    // Clear redo stack when new command is executed
    _redoStack.clear();
    
    // Limit history size
    if (_undoStack.length > maxHistorySize) {
      _undoStack.removeAt(0);
    }
    
    notifyListeners();
  }

  /// Undo last command
  void undo() {
    if (!canUndo || _currentState == null) return;
    
    final command = _undoStack.removeLast();
    final currentStateCopy = _currentState!.copy();
    
    // Restore previous state
    _currentState = command.undo(_currentState!);
    
    // Add to redo stack
    _redoStack.add(command.withPreviousState(currentStateCopy));
    
    notifyListeners();
  }

  /// Redo last undone command
  void redo() {
    if (!canRedo || _currentState == null) return;
    
    final command = _redoStack.removeLast();
    final currentStateCopy = _currentState!.copy();
    
    // Re-execute command
    _currentState = command.execute(_currentState!);
    
    // Add back to undo stack
    _undoStack.add(command.withPreviousState(currentStateCopy));
    
    notifyListeners();
  }

  /// Clear all history
  void clearHistory() {
    _undoStack.clear();
    _redoStack.clear();
    notifyListeners();
  }

  /// Get command history for debugging
  List<String> getCommandHistory() {
    return _undoStack.map((cmd) => cmd.description).toList();
  }
}

/// Base class for all project commands
abstract class ProjectCommand {
  final String description;
  ProjectState? _previousState;

  ProjectCommand(this.description);

  /// Execute the command
  ProjectState execute(ProjectState currentState);

  /// Undo the command
  ProjectState undo(ProjectState currentState) {
    return _previousState ?? currentState;
  }

  /// Set previous state for undo
  ProjectCommand withPreviousState(ProjectState previousState) {
    _previousState = previousState;
    return this;
  }
}

/// Command to update a video track
class UpdateVideoTrackCommand extends ProjectCommand {
  final String trackId;
  final VideoTrackModel updatedTrack;

  UpdateVideoTrackCommand(this.trackId, this.updatedTrack)
      : super('Update video track');

  @override
  ProjectState execute(ProjectState currentState) {
    final tracks = List<VideoTrackModel>.from(currentState.videoTracks);
    final index = tracks.indexWhere((track) => track.id == trackId);
    
    if (index != -1) {
      tracks[index] = updatedTrack;
    }
    
    return currentState.copyWith(videoTracks: tracks);
  }
}

/// Command to update an audio track
class UpdateAudioTrackCommand extends ProjectCommand {
  final String trackId;
  final AudioTrackModel updatedTrack;

  UpdateAudioTrackCommand(this.trackId, this.updatedTrack)
      : super('Update audio track');

  @override
  ProjectState execute(ProjectState currentState) {
    final tracks = List<AudioTrackModel>.from(currentState.audioTracks);
    final index = tracks.indexWhere((track) => track.id == trackId);
    
    if (index != -1) {
      tracks[index] = updatedTrack;
    }
    
    return currentState.copyWith(audioTracks: tracks);
  }
}

/// Command to update a text track
class UpdateTextTrackCommand extends ProjectCommand {
  final String trackId;
  final TextTrackModel updatedTrack;

  UpdateTextTrackCommand(this.trackId, this.updatedTrack)
      : super('Update text track');

  @override
  ProjectState execute(ProjectState currentState) {
    final tracks = List<TextTrackModel>.from(currentState.textTracks);
    final index = tracks.indexWhere((track) => track.id == trackId);
    
    if (index != -1) {
      tracks[index] = updatedTrack;
    }
    
    return currentState.copyWith(textTracks: tracks);
  }
}

/// Command to add a track
class AddTrackCommand extends ProjectCommand {
  final dynamic track;
  final TrackType trackType;

  AddTrackCommand(this.track, this.trackType)
      : super('Add ${trackType.name} track');

  @override
  ProjectState execute(ProjectState currentState) {
    switch (trackType) {
      case TrackType.video:
        final tracks = List<VideoTrackModel>.from(currentState.videoTracks);
        tracks.add(track as VideoTrackModel);
        return currentState.copyWith(videoTracks: tracks);
      case TrackType.audio:
        final tracks = List<AudioTrackModel>.from(currentState.audioTracks);
        tracks.add(track as AudioTrackModel);
        return currentState.copyWith(audioTracks: tracks);
      case TrackType.text:
        final tracks = List<TextTrackModel>.from(currentState.textTracks);
        tracks.add(track as TextTrackModel);
        return currentState.copyWith(textTracks: tracks);
      case TrackType.overlay:
        final tracks = List<OverlayVideoTrackModel>.from(currentState.overlayTracks);
        tracks.add(track as OverlayVideoTrackModel);
        return currentState.copyWith(overlayTracks: tracks);
    }
  }
}

/// Command to remove a track
class RemoveTrackCommand extends ProjectCommand {
  final String trackId;
  final TrackType trackType;

  RemoveTrackCommand(this.trackId, this.trackType)
      : super('Remove ${trackType.name} track');

  @override
  ProjectState execute(ProjectState currentState) {
    switch (trackType) {
      case TrackType.video:
        final tracks = List<VideoTrackModel>.from(currentState.videoTracks);
        tracks.removeWhere((track) => track.id == trackId);
        return currentState.copyWith(videoTracks: tracks);
      case TrackType.audio:
        final tracks = List<AudioTrackModel>.from(currentState.audioTracks);
        tracks.removeWhere((track) => track.id == trackId);
        return currentState.copyWith(audioTracks: tracks);
      case TrackType.text:
        final tracks = List<TextTrackModel>.from(currentState.textTracks);
        tracks.removeWhere((track) => track.id == trackId);
        return currentState.copyWith(textTracks: tracks);
      case TrackType.overlay:
        final tracks = List<OverlayVideoTrackModel>.from(currentState.overlayTracks);
        tracks.removeWhere((track) => track.id == trackId);
        return currentState.copyWith(overlayTracks: tracks);
    }
  }
}

/// Project state data class
class ProjectState {
  final String id;
  final String name;
  final List<VideoTrackModel> videoTracks;
  final List<AudioTrackModel> audioTracks;
  final List<TextTrackModel> textTracks;
  final List<OverlayVideoTrackModel> overlayTracks;
  final double totalDuration;
  final DateTime lastModified;
  final Map<String, dynamic> metadata;

  const ProjectState({
    required this.id,
    required this.name,
    required this.videoTracks,
    required this.audioTracks,
    required this.textTracks,
    required this.overlayTracks,
    required this.totalDuration,
    required this.lastModified,
    this.metadata = const {},
  });

  ProjectState copyWith({
    String? id,
    String? name,
    List<VideoTrackModel>? videoTracks,
    List<AudioTrackModel>? audioTracks,
    List<TextTrackModel>? textTracks,
    List<OverlayVideoTrackModel>? overlayTracks,
    double? totalDuration,
    DateTime? lastModified,
    Map<String, dynamic>? metadata,
  }) {
    return ProjectState(
      id: id ?? this.id,
      name: name ?? this.name,
      videoTracks: videoTracks ?? List.from(this.videoTracks),
      audioTracks: audioTracks ?? List.from(this.audioTracks),
      textTracks: textTracks ?? List.from(this.textTracks),
      overlayTracks: overlayTracks ?? List.from(this.overlayTracks),
      totalDuration: totalDuration ?? this.totalDuration,
      lastModified: lastModified ?? DateTime.now(),
      metadata: metadata ?? Map.from(this.metadata),
    );
  }

  ProjectState copy() {
    return copyWith();
  }

  /// Convert to JSON for persistence
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'videoTracks': videoTracks.map((track) => _videoTrackToJson(track)).toList(),
      'audioTracks': audioTracks.map((track) => _audioTrackToJson(track)).toList(),
      'textTracks': textTracks.map((track) => _textTrackToJson(track)).toList(),
      'overlayTracks': overlayTracks.map((track) => _overlayTrackToJson(track)).toList(),
      'totalDuration': totalDuration,
      'lastModified': lastModified.toIso8601String(),
      'metadata': metadata,
    };
  }

  /// Create from JSON
  factory ProjectState.fromJson(Map<String, dynamic> json) {
    return ProjectState(
      id: json['id'],
      name: json['name'],
      videoTracks: (json['videoTracks'] as List)
          .map((track) => _videoTrackFromJson(track))
          .toList(),
      audioTracks: (json['audioTracks'] as List)
          .map((track) => _audioTrackFromJson(track))
          .toList(),
      textTracks: (json['textTracks'] as List)
          .map((track) => _textTrackFromJson(track))
          .toList(),
      overlayTracks: (json['overlayTracks'] as List)
          .map((track) => _overlayTrackFromJson(track))
          .toList(),
      totalDuration: json['totalDuration'],
      lastModified: DateTime.parse(json['lastModified']),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  // Helper methods for JSON serialization (simplified)
  static Map<String, dynamic> _videoTrackToJson(VideoTrackModel track) {
    return {
      'id': track.id,
      'originalFile': track.originalFile.path,
      'processedFile': track.processedFile.path,
      'startTime': track.startTime,
      'endTime': track.endTime,
      // Add other properties as needed
    };
  }

  static VideoTrackModel _videoTrackFromJson(Map<String, dynamic> json) {
    return VideoTrackModel(
      id: json['id'],
      originalFile: File(json['originalFile']),
      processedFile: File(json['processedFile']),
      startTime: json['startTime'],
      endTime: json['endTime'],
      totalDuration: json['totalDuration'] ?? 0,
      // Add other properties as needed
    );
  }

  // Similar helper methods for other track types...
  static Map<String, dynamic> _audioTrackToJson(AudioTrackModel track) => {};
  static AudioTrackModel _audioTrackFromJson(Map<String, dynamic> json) => throw UnimplementedError();
  static Map<String, dynamic> _textTrackToJson(TextTrackModel track) => {};
  static TextTrackModel _textTrackFromJson(Map<String, dynamic> json) => throw UnimplementedError();
  static Map<String, dynamic> _overlayTrackToJson(OverlayVideoTrackModel track) => {};
  static OverlayVideoTrackModel _overlayTrackFromJson(Map<String, dynamic> json) => throw UnimplementedError();
}

/// Track types
enum TrackType {
  video,
  audio,
  text,
  overlay,
}

/// Project persistence manager
class ProjectPersistenceManager {
  static const String _projectsDirectory = 'video_editor_projects';

  /// Save project state to file
  static Future<void> saveProjectState(ProjectState state) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final projectsDir = Directory('${directory.path}/$_projectsDirectory');
      
      if (!await projectsDir.exists()) {
        await projectsDir.create(recursive: true);
      }
      
      final file = File('${projectsDir.path}/${state.id}.json');
      final jsonString = jsonEncode(state.toJson());
      
      await file.writeAsString(jsonString);
      print('Project saved: ${file.path}');
    } catch (e) {
      print('Error saving project: $e');
      rethrow;
    }
  }

  /// Load project state from file
  static Future<ProjectState?> loadProjectState(String projectId) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$_projectsDirectory/$projectId.json');
      
      if (!await file.exists()) {
        return null;
      }
      
      final jsonString = await file.readAsString();
      final json = jsonDecode(jsonString);
      
      return ProjectState.fromJson(json);
    } catch (e) {
      print('Error loading project: $e');
      return null;
    }
  }

  /// Auto-save with debouncing
  static Future<void> autoSave(ProjectState state) async {
    // Implement debounced auto-save logic here
    await saveProjectState(state);
  }
}
