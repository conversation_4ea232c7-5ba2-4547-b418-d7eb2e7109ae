import 'dart:io';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:ai_video_creator_editor/screens/project/models/video_track_model.dart';
import 'package:ai_video_creator_editor/screens/project/models/audio_track_model.dart';
import 'package:ai_video_creator_editor/screens/project/models/text_track_model.dart';
import 'package:ai_video_creator_editor/screens/project/models/overlay_video_track_model.dart';
import 'project_output_manager.dart';

/// Comprehensive RenderService with buildFilterComplex function
/// Generates FFmpeg nodes per clip with proper video/audio handling
class RenderService {
  /// Build complete filter complex for timeline
  static String buildFilterComplex({
    required List<VideoTrackModel> videoTracks,
    required List<AudioTrackModel> audioTracks,
    required List<TextTrackModel> textTracks,
    required List<OverlayVideoTrackModel> overlayTracks,
    required Size outputSize,
    double frameRate = 30.0,
    bool enableAudio = true,
  }) {
    final filters = <String>[];
    final videoInputs = <String>[];
    final audioInputs = <String>[];

    // Process video tracks
    for (int i = 0; i < videoTracks.length; i++) {
      final track = videoTracks[i];
      final inputLabel = '[$i:v]';
      final outputLabel = '[v$i]';

      final videoFilter = _buildVideoTrackFilter(
        track: track,
        inputLabel: inputLabel,
        outputLabel: outputLabel,
        outputSize: outputSize,
        frameRate: frameRate,
      );

      if (videoFilter.isNotEmpty) {
        filters.add(videoFilter);
        videoInputs.add(outputLabel);
      }
    }

    // Process audio tracks
    if (enableAudio) {
      for (int i = 0; i < audioTracks.length; i++) {
        final track = audioTracks[i];
        final inputLabel = '[$i:a]';
        final outputLabel = '[a$i]';

        final audioFilter = _buildAudioTrackFilter(
          track: track,
          inputLabel: inputLabel,
          outputLabel: outputLabel,
        );

        if (audioFilter.isNotEmpty) {
          filters.add(audioFilter);
          audioInputs.add(outputLabel);
        }
      }
    }

    // Process text overlays
    final textOverlayFilter = _buildTextOverlayFilter(
      textTracks: textTracks,
      videoInput: videoInputs.isNotEmpty ? videoInputs.last : '[0:v]',
      outputSize: outputSize,
    );

    if (textOverlayFilter.isNotEmpty) {
      filters.add(textOverlayFilter);
      if (videoInputs.isNotEmpty) {
        videoInputs[videoInputs.length - 1] = '[vtext]';
      } else {
        videoInputs.add('[vtext]');
      }
    }

    // Process overlay videos
    final overlayFilter = _buildOverlayVideoFilter(
      overlayTracks: overlayTracks,
      videoInput: videoInputs.isNotEmpty ? videoInputs.last : '[0:v]',
      outputSize: outputSize,
    );

    if (overlayFilter.isNotEmpty) {
      filters.add(overlayFilter);
      if (videoInputs.isNotEmpty) {
        videoInputs[videoInputs.length - 1] = '[voverlay]';
      } else {
        videoInputs.add('[voverlay]');
      }
    }

    // Concatenate or merge video tracks
    if (videoInputs.length > 1) {
      final concatFilter = _buildVideoConcatenationFilter(videoInputs);
      filters.add(concatFilter);
    } else if (videoInputs.isNotEmpty) {
      // Rename single video output
      filters.add('${videoInputs.first}copy[vout]');
    }

    // Mix audio tracks
    if (audioInputs.length > 1) {
      final audioMixFilter = _buildAudioMixFilter(audioInputs);
      filters.add(audioMixFilter);
    } else if (audioInputs.isNotEmpty) {
      // Rename single audio output
      filters.add('${audioInputs.first}copy[aout]');
    }

    return filters.join(';');
  }

  /// Build filter for individual video track
  static String _buildVideoTrackFilter({
    required VideoTrackModel track,
    required String inputLabel,
    required String outputLabel,
    required Size outputSize,
    double frameRate = 30.0,
  }) {
    final filterChain = <String>[];
    String currentInput = inputLabel;

    // Apply trim if needed
    if (track.videoTrimStart > 0 || track.videoTrimEnd > 0) {
      final trimFilter =
          'trim=start=${track.videoTrimStart}:end=${track.videoTrimEnd}';
      filterChain.add('$currentInput$trimFilter[trimmed]');
      currentInput = '[trimmed]';
    }

    // Apply crop if enabled
    if (track.cropModel?.enabled == true && track.cropModel!.isValid) {
      final cropFilter = track.cropModel!.toFFmpegFilter();
      filterChain.add('$currentInput$cropFilter[cropped]');
      currentInput = '[cropped]';
    }

    // Apply rotation
    if (track.rotation != 0) {
      final rotateFilter = _getRotationFilter(track.rotation);
      if (rotateFilter.isNotEmpty) {
        filterChain.add('$currentInput$rotateFilter[rotated]');
        currentInput = '[rotated]';
      }
    }

    // Apply video filters
    if (track.filter != null &&
        track.filter != 'None' &&
        track.filter!.isNotEmpty) {
      filterChain.add('$currentInput${track.filter}[filtered]');
      currentInput = '[filtered]';
    }

    // Apply playback speed
    if (track.playbackSpeed != 1.0) {
      final speedFilter = 'setpts=${1.0 / track.playbackSpeed}*PTS';
      filterChain.add('$currentInput$speedFilter[speed]');
      currentInput = '[speed]';
    }

    // Apply transform (scale, position)
    if (track.transform != null) {
      final transformFilter = _buildTransformFilter(track.transform!);
      if (transformFilter.isNotEmpty) {
        filterChain.add('$currentInput$transformFilter[transformed]');
        currentInput = '[transformed]';
      }
    }

    // Scale and pad to output size (always last)
    final scaleFilter = ProjectOutputManager.generateScaleFilter(outputSize);
    filterChain.add('$currentInput$scaleFilter,setsar=1$outputLabel');

    return filterChain.join(';');
  }

  /// Build filter for individual audio track
  static String _buildAudioTrackFilter({
    required AudioTrackModel track,
    required String inputLabel,
    required String outputLabel,
  }) {
    final filterChain = <String>[];
    String currentInput = inputLabel;

    // Apply audio trim
    if (track.trimStartTime > 0 || track.trimEndTime > 0) {
      final trimFilter =
          'atrim=start=${track.trimStartTime}:end=${track.trimEndTime}';
      filterChain.add('$currentInput$trimFilter[atrimmed]');
      currentInput = '[atrimmed]';
    }

    // Reset timestamps
    filterChain.add('${currentInput}asetpts=PTS-STARTPTS[areset]');
    currentInput = '[areset]';

    // Apply volume (default to 1.0 if not available)
    // Note: AudioTrackModel doesn't have volume property yet, using default
    const double volume = 1.0;
    if (volume != 1.0) {
      final volumeFilter = 'volume=$volume';
      filterChain.add('$currentInput$volumeFilter[avol]');
      currentInput = '[avol]';
    }

    // Apply fade in/out if specified
    // Note: AudioTrackModel doesn't have fade properties yet, using defaults
    const double fadeInDuration = 0.0;
    const double fadeOutDuration = 0.0;

    if (fadeInDuration > 0) {
      final fadeInFilter = 'afade=t=in:ss=0:d=$fadeInDuration';
      filterChain.add('$currentInput$fadeInFilter[afadein]');
      currentInput = '[afadein]';
    }

    if (fadeOutDuration > 0) {
      final duration = track.trimEndTime - track.trimStartTime;
      final fadeOutStart = duration - fadeOutDuration;
      final fadeOutFilter = 'afade=t=out:st=$fadeOutStart:d=$fadeOutDuration';
      filterChain.add('$currentInput$fadeOutFilter[afadeout]');
      currentInput = '[afadeout]';
    }

    // Final output
    filterChain.add('${currentInput}copy$outputLabel');

    return filterChain.join(';');
  }

  /// Build text overlay filter
  static String _buildTextOverlayFilter({
    required List<TextTrackModel> textTracks,
    required String videoInput,
    required Size outputSize,
  }) {
    if (textTracks.isEmpty) return '';

    final filterChain = <String>[];
    String currentInput = videoInput;

    for (int i = 0; i < textTracks.length; i++) {
      final track = textTracks[i];
      final outputLabel = i == textTracks.length - 1 ? '[vtext]' : '[text$i]';

      final textFilter = _buildDrawTextFilter(track, outputSize);
      final enableCondition =
          'enable=between(t,${track.trimStartTime},${track.trimEndTime})';

      filterChain.add('$currentInput$textFilter:$enableCondition$outputLabel');
      currentInput = outputLabel;
    }

    return filterChain.join(';');
  }

  /// Build overlay video filter
  static String _buildOverlayVideoFilter({
    required List<OverlayVideoTrackModel> overlayTracks,
    required String videoInput,
    required Size outputSize,
  }) {
    if (overlayTracks.isEmpty) return '';

    final filterChain = <String>[];
    String currentInput = videoInput;

    for (int i = 0; i < overlayTracks.length; i++) {
      final track = overlayTracks[i];
      final overlayInput = '[${overlayTracks.length + i}:v]';
      final outputLabel =
          i == overlayTracks.length - 1 ? '[voverlay]' : '[overlay$i]';

      final overlayFilter = _buildOverlayFilter(track, outputSize);
      final enableCondition =
          'enable=between(t,${track.trimStartTime},${track.trimEndTime})';

      filterChain.add(
          '$currentInput$overlayInput$overlayFilter:$enableCondition$outputLabel');
      currentInput = outputLabel;
    }

    return filterChain.join(';');
  }

  /// Build video concatenation filter
  static String _buildVideoConcatenationFilter(List<String> videoInputs) {
    final inputs = videoInputs.join('');
    return '${inputs}concat=n=${videoInputs.length}:v=1:a=0[vout]';
  }

  /// Build audio mix filter
  static String _buildAudioMixFilter(List<String> audioInputs) {
    if (audioInputs.length == 1) {
      return '${audioInputs.first}copy[aout]';
    }

    final inputs = audioInputs.join('');
    return '${inputs}amix=inputs=${audioInputs.length}:duration=longest[aout]';
  }

  /// Get rotation filter
  static String _getRotationFilter(int rotation) {
    switch (rotation % 360) {
      case 90:
        return 'transpose=1';
      case 180:
        return 'transpose=1,transpose=1';
      case 270:
        return 'transpose=2';
      default:
        return '';
    }
  }

  /// Build transform filter
  static String _buildTransformFilter(TransformData transform) {
    final filters = <String>[];

    if (transform.scale != 1.0) {
      filters.add('scale=iw*${transform.scale}:ih*${transform.scale}');
    }

    if (transform.x != 0.0 || transform.y != 0.0) {
      filters.add(
          'pad=iw+${transform.x.abs()}:ih+${transform.y.abs()}:${transform.x}:${transform.y}');
    }

    return filters.join(',');
  }

  /// Build drawtext filter for text overlay
  static String _buildDrawTextFilter(TextTrackModel track, Size outputSize) {
    final escapedText =
        track.text.replaceAll(':', '\\:').replaceAll("'", "\\'");
    final fontSize =
        (track.fontSize * outputSize.height / 1080).toInt(); // Scale font size

    return 'drawtext=text=\'$escapedText\':'
        'fontsize=$fontSize:'
        'fontcolor=${_colorToHex(track.textColor)}:'
        'x=${track.position.dx}:'
        'y=${track.position.dy}:'
        'fontfile=${track.fontFamily}';
  }

  /// Build overlay filter for video overlay
  static String _buildOverlayFilter(
      OverlayVideoTrackModel track, Size outputSize) {
    final position = track.position ?? Rect.zero;
    return 'overlay=${position.left}:${position.top}';
  }

  /// Convert Color to hex string
  static String _colorToHex(Color color) {
    return '#${color.value.toRadixString(16).padLeft(8, '0').substring(2)}';
  }

  /// Generate complete FFmpeg command
  static String generateFFmpegCommand({
    required List<String> inputPaths,
    required String outputPath,
    required String filterComplex,
    String codec = 'libx264',
    String preset = 'medium',
    int crf = 23,
    double frameRate = 30.0,
    String pixelFormat = 'yuv420p',
    bool enableAudio = true,
  }) {
    final inputs = inputPaths.map((path) => '-i "$path"').join(' ');
    final audioMap = enableAudio ? '-map "[aout]"' : '';

    return '-y $inputs '
        '-filter_complex "$filterComplex" '
        '-map "[vout]" $audioMap '
        '-c:v $codec -preset $preset -crf $crf '
        '-r $frameRate -pix_fmt $pixelFormat '
        '${enableAudio ? '-c:a aac -b:a 128k' : ''} '
        '-movflags +faststart '
        '"$outputPath"';
  }

  /// Validate filter complex syntax
  static bool validateFilterComplex(String filterComplex) {
    if (filterComplex.isEmpty) return false;

    // Basic validation checks
    final hasVideoOutput = filterComplex.contains('[vout]');
    final hasBalancedBrackets = _hasBalancedBrackets(filterComplex);
    final hasValidSyntax =
        !filterComplex.contains(';;') && !filterComplex.startsWith(';');

    return hasVideoOutput && hasBalancedBrackets && hasValidSyntax;
  }

  /// Check if brackets are balanced
  static bool _hasBalancedBrackets(String text) {
    int count = 0;
    for (int i = 0; i < text.length; i++) {
      if (text[i] == '[') count++;
      if (text[i] == ']') count--;
      if (count < 0) return false;
    }
    return count == 0;
  }

  /// Get estimated processing time
  static Duration estimateProcessingTime({
    required double totalDuration,
    required int videoTrackCount,
    required int audioTrackCount,
    required int textTrackCount,
    required int overlayTrackCount,
    required Size outputSize,
  }) {
    // Base processing time per second of video
    double baseTimePerSecond = 0.5;

    // Complexity multipliers
    final videoComplexity = 1.0 + (videoTrackCount - 1) * 0.3;
    final audioComplexity = 1.0 + audioTrackCount * 0.1;
    final textComplexity = 1.0 + textTrackCount * 0.2;
    final overlayComplexity = 1.0 + overlayTrackCount * 0.4;
    final resolutionComplexity =
        (outputSize.width * outputSize.height) / (1920 * 1080);

    final totalComplexity = videoComplexity *
        audioComplexity *
        textComplexity *
        overlayComplexity *
        resolutionComplexity;

    final estimatedSeconds =
        totalDuration * baseTimePerSecond * totalComplexity;

    return Duration(seconds: estimatedSeconds.ceil());
  }
}
