import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Advanced multi-touch gesture detector for overlay manipulation
/// Supports simultaneous scaling, rotation, and translation with proper gesture recognition
class MultiTouchGestureDetector extends StatefulWidget {
  final Widget child;
  final Function(ScaleStartDetails)? onScaleStart;
  final Function(ScaleUpdateDetails)? onScaleUpdate;
  final Function(ScaleEndDetails)? onScaleEnd;
  final Function(TapDownDetails)? onTapDown;
  final Function(DragStartDetails)? onPanStart;
  final Function(DragUpdateDetails)? onPanUpdate;
  final Function(DragEndDetails)? onPanEnd;
  final bool enableScale;
  final bool enableRotation;
  final bool enableTranslation;
  final double minScale;
  final double maxScale;

  const MultiTouchGestureDetector({
    Key? key,
    required this.child,
    this.onScaleStart,
    this.onScaleUpdate,
    this.onScaleEnd,
    this.onTapDown,
    this.onPanStart,
    this.onPanUpdate,
    this.onPanEnd,
    this.enableScale = true,
    this.enableRotation = true,
    this.enableTranslation = true,
    this.minScale = 0.1,
    this.maxScale = 5.0,
  }) : super(key: key);

  @override
  State<MultiTouchGestureDetector> createState() => _MultiTouchGestureDetectorState();
}

class _MultiTouchGestureDetectorState extends State<MultiTouchGestureDetector> {
  // Gesture state
  bool _isScaling = false;
  bool _isRotating = false;
  bool _isTranslating = false;
  
  // Initial values for gestures
  double _initialScale = 1.0;
  double _initialRotation = 0.0;
  Offset _initialFocalPoint = Offset.zero;
  
  // Current transformation values
  double _currentScale = 1.0;
  double _currentRotation = 0.0;
  Offset _currentTranslation = Offset.zero;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: widget.onTapDown,
      onPanStart: _handlePanStart,
      onPanUpdate: _handlePanUpdate,
      onPanEnd: _handlePanEnd,
      onScaleStart: _handleScaleStart,
      onScaleUpdate: _handleScaleUpdate,
      onScaleEnd: _handleScaleEnd,
      child: widget.child,
    );
  }

  /// Handle scale gesture start
  void _handleScaleStart(ScaleStartDetails details) {
    setState(() {
      _isScaling = details.pointerCount > 1;
      _isRotating = details.pointerCount > 1 && widget.enableRotation;
      _initialScale = _currentScale;
      _initialRotation = _currentRotation;
      _initialFocalPoint = details.focalPoint;
    });
    
    widget.onScaleStart?.call(details);
  }

  /// Handle scale gesture update
  void _handleScaleUpdate(ScaleUpdateDetails details) {
    if (!_isScaling && !_isRotating) return;
    
    // Handle scaling
    if (_isScaling && widget.enableScale) {
      final newScale = (_initialScale * details.scale).clamp(widget.minScale, widget.maxScale);
      setState(() {
        _currentScale = newScale;
      });
    }
    
    // Handle rotation
    if (_isRotating && widget.enableRotation) {
      final newRotation = _initialRotation + details.rotation;
      setState(() {
        _currentRotation = newRotation;
      });
    }
    
    // Handle translation during multi-touch
    if (widget.enableTranslation) {
      final focalPointDelta = details.focalPoint - _initialFocalPoint;
      setState(() {
        _currentTranslation = focalPointDelta;
      });
    }
    
    // Create custom scale update details with our values
    final customDetails = ScaleUpdateDetails(
      focalPoint: details.focalPoint,
      localFocalPoint: details.localFocalPoint,
      scale: _currentScale,
      horizontalScale: details.horizontalScale,
      verticalScale: details.verticalScale,
      rotation: _currentRotation,
      pointerCount: details.pointerCount,
    );
    
    widget.onScaleUpdate?.call(customDetails);
  }

  /// Handle scale gesture end
  void _handleScaleEnd(ScaleEndDetails details) {
    setState(() {
      _isScaling = false;
      _isRotating = false;
      _initialScale = _currentScale;
      _initialRotation = _currentRotation;
    });
    
    widget.onScaleEnd?.call(details);
  }

  /// Handle pan gesture start (single touch)
  void _handlePanStart(DragStartDetails details) {
    if (_isScaling || _isRotating) return;
    
    setState(() {
      _isTranslating = widget.enableTranslation;
    });
    
    widget.onPanStart?.call(details);
  }

  /// Handle pan gesture update (single touch)
  void _handlePanUpdate(DragUpdateDetails details) {
    if (!_isTranslating || _isScaling || _isRotating) return;
    
    widget.onPanUpdate?.call(details);
  }

  /// Handle pan gesture end (single touch)
  void _handlePanEnd(DragEndDetails details) {
    setState(() {
      _isTranslating = false;
    });
    
    widget.onPanEnd?.call(details);
  }
}

/// Advanced overlay manipulation widget that combines bounding box and multi-touch gestures
class AdvancedOverlayManipulator extends StatefulWidget {
  final Widget child;
  final Rect initialBounds;
  final double initialRotation;
  final double initialScale;
  final bool isSelected;
  final Function(OverlayTransform transform)? onTransformChanged;
  final VoidCallback? onTap;
  final bool enableResize;
  final bool enableRotation;
  final bool enableMove;
  final bool enableScale;

  const AdvancedOverlayManipulator({
    Key? key,
    required this.child,
    required this.initialBounds,
    this.initialRotation = 0.0,
    this.initialScale = 1.0,
    this.isSelected = false,
    this.onTransformChanged,
    this.onTap,
    this.enableResize = true,
    this.enableRotation = true,
    this.enableMove = true,
    this.enableScale = true,
  }) : super(key: key);

  @override
  State<AdvancedOverlayManipulator> createState() => _AdvancedOverlayManipulatorState();
}

class _AdvancedOverlayManipulatorState extends State<AdvancedOverlayManipulator> {
  late OverlayTransform _currentTransform;
  bool _isManipulating = false;

  @override
  void initState() {
    super.initState();
    _currentTransform = OverlayTransform(
      bounds: widget.initialBounds,
      rotation: widget.initialRotation,
      scale: widget.initialScale,
      translation: Offset.zero,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: _currentTransform.bounds.left,
      top: _currentTransform.bounds.top,
      child: Transform.translate(
        offset: _currentTransform.translation,
        child: Transform.rotate(
          angle: _currentTransform.rotation,
          child: Transform.scale(
            scale: _currentTransform.scale,
            child: MultiTouchGestureDetector(
              onTapDown: _handleTap,
              onScaleStart: _handleScaleStart,
              onScaleUpdate: _handleScaleUpdate,
              onScaleEnd: _handleScaleEnd,
              onPanStart: _handlePanStart,
              onPanUpdate: _handlePanUpdate,
              onPanEnd: _handlePanEnd,
              enableScale: widget.enableScale,
              enableRotation: widget.enableRotation,
              enableTranslation: widget.enableMove,
              child: Container(
                width: _currentTransform.bounds.width,
                height: _currentTransform.bounds.height,
                decoration: widget.isSelected
                    ? BoxDecoration(
                        border: Border.all(color: Colors.blue, width: 2),
                        borderRadius: BorderRadius.circular(4),
                      )
                    : null,
                child: widget.child,
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _handleTap(TapDownDetails details) {
    widget.onTap?.call();
  }

  void _handleScaleStart(ScaleStartDetails details) {
    setState(() {
      _isManipulating = true;
    });
  }

  void _handleScaleUpdate(ScaleUpdateDetails details) {
    if (!_isManipulating) return;

    setState(() {
      _currentTransform = _currentTransform.copyWith(
        scale: details.scale,
        rotation: details.rotation,
        translation: details.focalPoint - _currentTransform.bounds.center,
      );
    });

    widget.onTransformChanged?.call(_currentTransform);
  }

  void _handleScaleEnd(ScaleEndDetails details) {
    setState(() {
      _isManipulating = false;
    });
  }

  void _handlePanStart(DragStartDetails details) {
    setState(() {
      _isManipulating = true;
    });
  }

  void _handlePanUpdate(DragUpdateDetails details) {
    if (!_isManipulating) return;

    setState(() {
      _currentTransform = _currentTransform.copyWith(
        translation: _currentTransform.translation + details.delta,
      );
    });

    widget.onTransformChanged?.call(_currentTransform);
  }

  void _handlePanEnd(DragEndDetails details) {
    setState(() {
      _isManipulating = false;
    });
  }
}

/// Data class for overlay transformation
class OverlayTransform {
  final Rect bounds;
  final double rotation;
  final double scale;
  final Offset translation;

  const OverlayTransform({
    required this.bounds,
    this.rotation = 0.0,
    this.scale = 1.0,
    this.translation = Offset.zero,
  });

  OverlayTransform copyWith({
    Rect? bounds,
    double? rotation,
    double? scale,
    Offset? translation,
  }) {
    return OverlayTransform(
      bounds: bounds ?? this.bounds,
      rotation: rotation ?? this.rotation,
      scale: scale ?? this.scale,
      translation: translation ?? this.translation,
    );
  }

  /// Get the final transformed bounds
  Rect get transformedBounds {
    final center = bounds.center + translation;
    final scaledWidth = bounds.width * scale;
    final scaledHeight = bounds.height * scale;
    
    return Rect.fromCenter(
      center: center,
      width: scaledWidth,
      height: scaledHeight,
    );
  }

  /// Convert to transformation matrix
  Matrix4 get matrix {
    final matrix = Matrix4.identity();
    
    // Apply translation
    matrix.translate(translation.dx, translation.dy);
    
    // Apply rotation around center
    final center = bounds.center;
    matrix.translate(center.dx, center.dy);
    matrix.rotateZ(rotation);
    matrix.translate(-center.dx, -center.dy);
    
    // Apply scale around center
    matrix.translate(center.dx, center.dy);
    matrix.scale(scale, scale);
    matrix.translate(-center.dx, -center.dy);
    
    return matrix;
  }

  @override
  String toString() {
    return 'OverlayTransform(bounds: $bounds, rotation: $rotation, scale: $scale, translation: $translation)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OverlayTransform &&
        other.bounds == bounds &&
        other.rotation == rotation &&
        other.scale == scale &&
        other.translation == translation;
  }

  @override
  int get hashCode {
    return bounds.hashCode ^
        rotation.hashCode ^
        scale.hashCode ^
        translation.hashCode;
  }
}
