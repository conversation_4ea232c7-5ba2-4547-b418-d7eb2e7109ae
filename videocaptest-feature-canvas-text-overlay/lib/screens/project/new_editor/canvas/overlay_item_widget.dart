import 'package:flutter/material.dart';
import 'dart:io';
import 'dart:math' as math;

/// Types of overlay content
enum OverlayType {
  text,
  image,
  video,
  shape,
  sticker,
}

/// Widget for rendering different types of overlay items
/// Handles text, images, videos, shapes, and stickers with proper styling
class OverlayItemWidget extends StatelessWidget {
  final OverlayType type;
  final String content;
  final TextStyle? style;
  final bool isSelected;
  final double opacity;
  final BlendMode? blendMode;
  final Color? backgroundColor;
  final double? width;
  final double? height;
  final BorderRadius? borderRadius;
  final BoxShadow? shadow;

  const OverlayItemWidget({
    Key? key,
    required this.type,
    required this.content,
    this.style,
    this.isSelected = false,
    this.opacity = 1.0,
    this.blendMode,
    this.backgroundColor,
    this.width,
    this.height,
    this.borderRadius,
    this.shadow,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Widget child;

    switch (type) {
      case OverlayType.text:
        child = _buildTextOverlay();
        break;
      case OverlayType.image:
        child = _buildImageOverlay();
        break;
      case OverlayType.video:
        child = _buildVideoOverlay();
        break;
      case OverlayType.shape:
        child = _buildShapeOverlay();
        break;
      case OverlayType.sticker:
        child = _buildStickerOverlay();
        break;
    }

    // Wrap with selection indicator and effects
    return _wrapWithEffects(child);
  }

  /// Build text overlay
  Widget _buildTextOverlay() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.transparent,
        borderRadius: borderRadius ?? BorderRadius.circular(4),
        boxShadow: shadow != null ? [shadow!] : null,
      ),
      child: Text(
        content,
        style: style ?? const TextStyle(
          color: Colors.white,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  /// Build image overlay
  Widget _buildImageOverlay() {
    final file = File(content);
    
    if (!file.existsSync()) {
      return _buildErrorPlaceholder('Image not found');
    }

    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: borderRadius,
        boxShadow: shadow != null ? [shadow!] : null,
      ),
      child: ClipRRect(
        borderRadius: borderRadius ?? BorderRadius.zero,
        child: Image.file(
          file,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return _buildErrorPlaceholder('Failed to load image');
          },
        ),
      ),
    );
  }

  /// Build video overlay
  Widget _buildVideoOverlay() {
    // For now, show a placeholder. In a full implementation,
    // this would use a video player widget
    return Container(
      width: width ?? 200,
      height: height ?? 150,
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: borderRadius ?? BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withOpacity(0.5)),
        boxShadow: shadow != null ? [shadow!] : null,
      ),
      child: const Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.play_circle_outline,
              color: Colors.white,
              size: 32,
            ),
            SizedBox(height: 4),
            Text(
              'Video Overlay',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build shape overlay
  Widget _buildShapeOverlay() {
    // Parse shape type from content
    final shapeType = content.toLowerCase();
    
    return Container(
      width: width ?? 100,
      height: height ?? 100,
      child: CustomPaint(
        painter: ShapePainter(
          shapeType: shapeType,
          color: style?.color ?? Colors.blue,
          strokeWidth: 2.0,
        ),
      ),
    );
  }

  /// Build sticker overlay
  Widget _buildStickerOverlay() {
    // For stickers, we can use emoji or image files
    if (content.length <= 4 && _isEmoji(content)) {
      return Text(
        content,
        style: TextStyle(
          fontSize: style?.fontSize ?? 32,
        ),
      );
    } else {
      // Treat as image file
      return _buildImageOverlay();
    }
  }

  /// Wrap overlay with effects and selection indicator
  Widget _wrapWithEffects(Widget child) {
    Widget wrappedChild = child;

    // Apply opacity
    if (opacity < 1.0) {
      wrappedChild = Opacity(
        opacity: opacity,
        child: wrappedChild,
      );
    }

    // Apply blend mode
    if (blendMode != null) {
      wrappedChild = BlendMask(
        blendMode: blendMode!,
        child: wrappedChild,
      );
    }

    // Add selection indicator
    if (isSelected) {
      wrappedChild = Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: Colors.blue,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(4),
        ),
        child: wrappedChild,
      );
    }

    return wrappedChild;
  }

  /// Build error placeholder
  Widget _buildErrorPlaceholder(String message) {
    return Container(
      width: width ?? 100,
      height: height ?? 100,
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        border: Border.all(color: Colors.red.withOpacity(0.5)),
        borderRadius: borderRadius ?? BorderRadius.circular(4),
      ),
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 24,
            ),
            const SizedBox(height: 4),
            Text(
              message,
              style: const TextStyle(
                color: Colors.red,
                fontSize: 10,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Check if string is an emoji
  bool _isEmoji(String text) {
    final emojiRegex = RegExp(
      r'[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]',
      unicode: true,
    );
    return emojiRegex.hasMatch(text);
  }
}

/// Custom painter for drawing shapes
class ShapePainter extends CustomPainter {
  final String shapeType;
  final Color color;
  final double strokeWidth;

  ShapePainter({
    required this.shapeType,
    required this.color,
    this.strokeWidth = 2.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = math.min(size.width, size.height) / 2 - strokeWidth;

    switch (shapeType) {
      case 'circle':
        canvas.drawCircle(center, radius, paint);
        break;
      case 'rectangle':
      case 'rect':
        final rect = Rect.fromCenter(
          center: center,
          width: size.width - strokeWidth * 2,
          height: size.height - strokeWidth * 2,
        );
        canvas.drawRect(rect, paint);
        break;
      case 'triangle':
        final path = Path();
        path.moveTo(center.dx, strokeWidth);
        path.lineTo(strokeWidth, size.height - strokeWidth);
        path.lineTo(size.width - strokeWidth, size.height - strokeWidth);
        path.close();
        canvas.drawPath(path, paint);
        break;
      case 'star':
        _drawStar(canvas, center, radius, paint);
        break;
      case 'arrow':
        _drawArrow(canvas, size, paint);
        break;
      default:
        // Default to circle
        canvas.drawCircle(center, radius, paint);
    }
  }

  void _drawStar(Canvas canvas, Offset center, double radius, Paint paint) {
    final path = Path();
    const int points = 5;
    const double angle = math.pi / points;

    for (int i = 0; i < points * 2; i++) {
      final double r = i.isEven ? radius : radius * 0.5;
      final double x = center.dx + r * math.cos(i * angle - math.pi / 2);
      final double y = center.dy + r * math.sin(i * angle - math.pi / 2);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();
    canvas.drawPath(path, paint);
  }

  void _drawArrow(Canvas canvas, Size size, Paint paint) {
    final path = Path();
    final double arrowWidth = size.width * 0.8;
    final double arrowHeight = size.height * 0.6;
    final double headWidth = size.width * 0.4;
    final double headHeight = size.height * 0.4;

    // Arrow body
    path.moveTo((size.width - arrowWidth) / 2, (size.height - arrowHeight) / 2);
    path.lineTo((size.width + arrowWidth) / 2 - headWidth, (size.height - arrowHeight) / 2);
    path.lineTo((size.width + arrowWidth) / 2 - headWidth, (size.height - headHeight) / 2);
    
    // Arrow head
    path.lineTo((size.width + arrowWidth) / 2, size.height / 2);
    path.lineTo((size.width + arrowWidth) / 2 - headWidth, (size.height + headHeight) / 2);
    path.lineTo((size.width + arrowWidth) / 2 - headWidth, (size.height + arrowHeight) / 2);
    path.lineTo((size.width - arrowWidth) / 2, (size.height + arrowHeight) / 2);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(ShapePainter oldDelegate) {
    return oldDelegate.shapeType != shapeType ||
        oldDelegate.color != color ||
        oldDelegate.strokeWidth != strokeWidth;
  }
}

/// Widget for applying blend modes
class BlendMask extends StatelessWidget {
  final BlendMode blendMode;
  final Widget child;

  const BlendMask({
    Key? key,
    required this.blendMode,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ColorFiltered(
      colorFilter: ColorFilter.mode(
        Colors.transparent,
        blendMode,
      ),
      child: child,
    );
  }
}
