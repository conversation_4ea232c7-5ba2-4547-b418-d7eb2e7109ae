import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Bounding box with draggable handles for overlay manipulation
/// Supports scaling, rotating, moving, and cropping with proper gesture detection
class BoundingBoxWidget extends StatefulWidget {
  final Widget child;
  final Rect bounds;
  final bool isSelected;
  final Function(Rect newBounds)? onBoundsChanged;
  final Function(double rotation)? onRotationChanged;
  final Function(Offset translation)? onTranslationChanged;
  final VoidCallback? onTap;
  final bool enableResize;
  final bool enableRotation;
  final bool enableMove;
  final bool enableCrop;
  final double minWidth;
  final double minHeight;
  final double maxWidth;
  final double maxHeight;

  const BoundingBoxWidget({
    Key? key,
    required this.child,
    required this.bounds,
    this.isSelected = false,
    this.onBoundsChanged,
    this.onRotationChanged,
    this.onTranslationChanged,
    this.onTap,
    this.enableResize = true,
    this.enableRotation = true,
    this.enableMove = true,
    this.enableCrop = false,
    this.minWidth = 20.0,
    this.minHeight = 20.0,
    this.maxWidth = double.infinity,
    this.maxHeight = double.infinity,
  }) : super(key: key);

  @override
  State<BoundingBoxWidget> createState() => _BoundingBoxWidgetState();
}

class _BoundingBoxWidgetState extends State<BoundingBoxWidget> {
  Rect _currentBounds = Rect.zero;
  double _currentRotation = 0.0;
  Offset _dragStart = Offset.zero;
  Rect _dragStartBounds = Rect.zero;
  
  // Handle types for different interactions
  HandleType? _activeHandle;
  bool _isDragging = false;
  bool _isRotating = false;

  @override
  void initState() {
    super.initState();
    _currentBounds = widget.bounds;
  }

  @override
  void didUpdateWidget(BoundingBoxWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.bounds != widget.bounds) {
      _currentBounds = widget.bounds;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: _currentBounds.left,
      top: _currentBounds.top,
      width: _currentBounds.width,
      height: _currentBounds.height,
      child: GestureDetector(
        onTap: widget.onTap,
        onPanStart: _handlePanStart,
        onPanUpdate: _handlePanUpdate,
        onPanEnd: _handlePanEnd,
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            // The actual content
            Positioned.fill(child: widget.child),
            
            // Selection overlay and handles
            if (widget.isSelected) ...[
              // Selection border
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Colors.blue,
                      width: 2,
                    ),
                  ),
                ),
              ),
              
              // Resize handles
              if (widget.enableResize) ..._buildResizeHandles(),
              
              // Rotation handle
              if (widget.enableRotation) _buildRotationHandle(),
              
              // Crop handles (if enabled)
              if (widget.enableCrop) ..._buildCropHandles(),
            ],
          ],
        ),
      ),
    );
  }

  /// Build resize handles at corners and edges
  List<Widget> _buildResizeHandles() {
    const handleSize = 12.0;
    const handleOffset = handleSize / 2;
    
    return [
      // Corner handles
      _buildHandle(
        HandleType.topLeft,
        Positioned(
          left: -handleOffset,
          top: -handleOffset,
          child: _createHandleWidget(handleSize, Colors.blue),
        ),
      ),
      _buildHandle(
        HandleType.topRight,
        Positioned(
          right: -handleOffset,
          top: -handleOffset,
          child: _createHandleWidget(handleSize, Colors.blue),
        ),
      ),
      _buildHandle(
        HandleType.bottomLeft,
        Positioned(
          left: -handleOffset,
          bottom: -handleOffset,
          child: _createHandleWidget(handleSize, Colors.blue),
        ),
      ),
      _buildHandle(
        HandleType.bottomRight,
        Positioned(
          right: -handleOffset,
          bottom: -handleOffset,
          child: _createHandleWidget(handleSize, Colors.blue),
        ),
      ),
      
      // Edge handles
      _buildHandle(
        HandleType.topCenter,
        Positioned(
          left: _currentBounds.width / 2 - handleOffset,
          top: -handleOffset,
          child: _createHandleWidget(handleSize, Colors.lightBlue),
        ),
      ),
      _buildHandle(
        HandleType.bottomCenter,
        Positioned(
          left: _currentBounds.width / 2 - handleOffset,
          bottom: -handleOffset,
          child: _createHandleWidget(handleSize, Colors.lightBlue),
        ),
      ),
      _buildHandle(
        HandleType.leftCenter,
        Positioned(
          left: -handleOffset,
          top: _currentBounds.height / 2 - handleOffset,
          child: _createHandleWidget(handleSize, Colors.lightBlue),
        ),
      ),
      _buildHandle(
        HandleType.rightCenter,
        Positioned(
          right: -handleOffset,
          top: _currentBounds.height / 2 - handleOffset,
          child: _createHandleWidget(handleSize, Colors.lightBlue),
        ),
      ),
    ];
  }

  /// Build rotation handle
  Widget _buildRotationHandle() {
    const handleSize = 16.0;
    const handleOffset = handleSize / 2;
    
    return _buildHandle(
      HandleType.rotation,
      Positioned(
        left: _currentBounds.width / 2 - handleOffset,
        top: -30,
        child: Container(
          width: handleSize,
          height: handleSize,
          decoration: const BoxDecoration(
            color: Colors.green,
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.rotate_right,
            size: 12,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  /// Build crop handles (for cropping functionality)
  List<Widget> _buildCropHandles() {
    const handleSize = 8.0;
    const handleOffset = handleSize / 2;
    
    return [
      // Crop indicators at corners
      Positioned(
        left: -handleOffset,
        top: -handleOffset,
        child: Container(
          width: handleSize,
          height: handleSize,
          decoration: const BoxDecoration(
            color: Colors.orange,
            shape: BoxShape.circle,
          ),
        ),
      ),
      Positioned(
        right: -handleOffset,
        top: -handleOffset,
        child: Container(
          width: handleSize,
          height: handleSize,
          decoration: const BoxDecoration(
            color: Colors.orange,
            shape: BoxShape.circle,
          ),
        ),
      ),
      Positioned(
        left: -handleOffset,
        bottom: -handleOffset,
        child: Container(
          width: handleSize,
          height: handleSize,
          decoration: const BoxDecoration(
            color: Colors.orange,
            shape: BoxShape.circle,
          ),
        ),
      ),
      Positioned(
        right: -handleOffset,
        bottom: -handleOffset,
        child: Container(
          width: handleSize,
          height: handleSize,
          decoration: const BoxDecoration(
            color: Colors.orange,
            shape: BoxShape.circle,
          ),
        ),
      ),
    ];
  }

  /// Build a handle with gesture detection
  Widget _buildHandle(HandleType type, Widget positionedHandle) {
    return GestureDetector(
      onPanStart: (details) => _handleHandlePanStart(type, details),
      onPanUpdate: _handleHandlePanUpdate,
      onPanEnd: _handleHandlePanEnd,
      child: positionedHandle,
    );
  }

  /// Create a handle widget
  Widget _createHandleWidget(double size, Color color) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
        border: Border.all(color: Colors.white, width: 1),
      ),
    );
  }

  /// Handle pan start for the main widget
  void _handlePanStart(DragStartDetails details) {
    if (!widget.enableMove) return;
    
    setState(() {
      _isDragging = true;
      _dragStart = details.localPosition;
      _dragStartBounds = _currentBounds;
    });
  }

  /// Handle pan update for the main widget
  void _handlePanUpdate(DragUpdateDetails details) {
    if (!_isDragging || !widget.enableMove) return;
    
    final delta = details.localPosition - _dragStart;
    final newBounds = Rect.fromLTWH(
      _dragStartBounds.left + delta.dx,
      _dragStartBounds.top + delta.dy,
      _dragStartBounds.width,
      _dragStartBounds.height,
    );
    
    setState(() {
      _currentBounds = newBounds;
    });
    
    widget.onTranslationChanged?.call(Offset(delta.dx, delta.dy));
  }

  /// Handle pan end for the main widget
  void _handlePanEnd(DragEndDetails details) {
    setState(() {
      _isDragging = false;
    });
    
    widget.onBoundsChanged?.call(_currentBounds);
  }

  /// Handle pan start for resize/rotation handles
  void _handleHandlePanStart(HandleType type, DragStartDetails details) {
    setState(() {
      _activeHandle = type;
      _dragStart = details.localPosition;
      _dragStartBounds = _currentBounds;
      
      if (type == HandleType.rotation) {
        _isRotating = true;
      }
    });
  }

  /// Handle pan update for resize/rotation handles
  void _handleHandlePanUpdate(DragUpdateDetails details) {
    if (_activeHandle == null) return;
    
    if (_isRotating) {
      _handleRotationUpdate(details);
    } else {
      _handleResizeUpdate(details);
    }
  }

  /// Handle pan end for resize/rotation handles
  void _handleHandlePanEnd(DragEndDetails details) {
    setState(() {
      _activeHandle = null;
      _isRotating = false;
    });
    
    widget.onBoundsChanged?.call(_currentBounds);
  }

  /// Handle rotation updates
  void _handleRotationUpdate(DragUpdateDetails details) {
    final center = Offset(
      _currentBounds.left + _currentBounds.width / 2,
      _currentBounds.top + _currentBounds.height / 2,
    );
    
    final angle = math.atan2(
      details.localPosition.dy - center.dy,
      details.localPosition.dx - center.dx,
    );
    
    setState(() {
      _currentRotation = angle;
    });
    
    widget.onRotationChanged?.call(angle);
  }

  /// Handle resize updates
  void _handleResizeUpdate(DragUpdateDetails details) {
    if (_activeHandle == null) return;
    
    final delta = details.localPosition - _dragStart;
    Rect newBounds = _dragStartBounds;
    
    switch (_activeHandle!) {
      case HandleType.topLeft:
        newBounds = Rect.fromLTRB(
          _dragStartBounds.left + delta.dx,
          _dragStartBounds.top + delta.dy,
          _dragStartBounds.right,
          _dragStartBounds.bottom,
        );
        break;
      case HandleType.topRight:
        newBounds = Rect.fromLTRB(
          _dragStartBounds.left,
          _dragStartBounds.top + delta.dy,
          _dragStartBounds.right + delta.dx,
          _dragStartBounds.bottom,
        );
        break;
      case HandleType.bottomLeft:
        newBounds = Rect.fromLTRB(
          _dragStartBounds.left + delta.dx,
          _dragStartBounds.top,
          _dragStartBounds.right,
          _dragStartBounds.bottom + delta.dy,
        );
        break;
      case HandleType.bottomRight:
        newBounds = Rect.fromLTRB(
          _dragStartBounds.left,
          _dragStartBounds.top,
          _dragStartBounds.right + delta.dx,
          _dragStartBounds.bottom + delta.dy,
        );
        break;
      case HandleType.topCenter:
        newBounds = Rect.fromLTRB(
          _dragStartBounds.left,
          _dragStartBounds.top + delta.dy,
          _dragStartBounds.right,
          _dragStartBounds.bottom,
        );
        break;
      case HandleType.bottomCenter:
        newBounds = Rect.fromLTRB(
          _dragStartBounds.left,
          _dragStartBounds.top,
          _dragStartBounds.right,
          _dragStartBounds.bottom + delta.dy,
        );
        break;
      case HandleType.leftCenter:
        newBounds = Rect.fromLTRB(
          _dragStartBounds.left + delta.dx,
          _dragStartBounds.top,
          _dragStartBounds.right,
          _dragStartBounds.bottom,
        );
        break;
      case HandleType.rightCenter:
        newBounds = Rect.fromLTRB(
          _dragStartBounds.left,
          _dragStartBounds.top,
          _dragStartBounds.right + delta.dx,
          _dragStartBounds.bottom,
        );
        break;
      case HandleType.rotation:
        // Handled separately
        break;
    }
    
    // Apply constraints
    newBounds = _constrainBounds(newBounds);
    
    setState(() {
      _currentBounds = newBounds;
    });
  }

  /// Constrain bounds to min/max limits
  Rect _constrainBounds(Rect bounds) {
    final width = bounds.width.clamp(widget.minWidth, widget.maxWidth);
    final height = bounds.height.clamp(widget.minHeight, widget.maxHeight);
    
    return Rect.fromLTWH(
      bounds.left,
      bounds.top,
      width,
      height,
    );
  }
}

/// Types of handles for different interactions
enum HandleType {
  topLeft,
  topRight,
  bottomLeft,
  bottomRight,
  topCenter,
  bottomCenter,
  leftCenter,
  rightCenter,
  rotation,
}
