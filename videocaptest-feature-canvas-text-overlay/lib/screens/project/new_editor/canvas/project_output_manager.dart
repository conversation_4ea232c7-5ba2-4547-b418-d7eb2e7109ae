import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Project output size management
/// Always locks project outputSize (e.g., 1080×1920 portrait) and maintains aspect ratio
class ProjectOutputManager {
  static const Map<String, Size> presetSizes = {
    // Portrait formats
    'Instagram Story': <PERSON><PERSON>(1080, 1920),
    'TikTok': <PERSON><PERSON>(1080, 1920),
    'YouTube Shorts': <PERSON><PERSON>(1080, 1920),
    'Snapchat': <PERSON><PERSON>(1080, 1920),
    
    // Landscape formats
    'YouTube': <PERSON><PERSON>(1920, 1080),
    'Instagram Landscape': <PERSON><PERSON>(1920, 1080),
    'Facebook Video': <PERSON><PERSON>(1920, 1080),
    'Twitter Video': Size(1920, 1080),
    
    // Square formats
    'Instagram Post': <PERSON><PERSON>(1080, 1080),
    'Facebook Post': <PERSON><PERSON>(1080, 1080),
    'Twitter Post': Size(1080, 1080),
    
    // Custom formats
    '4K Portrait': Size(2160, 3840),
    '4K Landscape': Size(3840, 2160),
    '4K Square': <PERSON>ze(2160, 2160),
  };

  /// Get aspect ratio from size
  static double getAspectRatio(Size size) {
    return size.width / size.height;
  }

  /// Get orientation from size
  static Orientation getOrientation(Size size) {
    if (size.width > size.height) {
      return Orientation.landscape;
    } else if (size.width < size.height) {
      return Orientation.portrait;
    } else {
      return Orientation.portrait; // Square treated as portrait
    }
  }

  /// Get preset name from size
  static String? getPresetName(Size size) {
    for (final entry in presetSizes.entries) {
      if (entry.value == size) {
        return entry.key;
      }
    }
    return null;
  }

  /// Validate if size is supported
  static bool isSizeSupported(Size size) {
    return size.width >= 480 && 
           size.height >= 480 && 
           size.width <= 4096 && 
           size.height <= 4096;
  }

  /// Get recommended sizes based on content type
  static List<Size> getRecommendedSizes(ContentType contentType) {
    switch (contentType) {
      case ContentType.socialMedia:
        return [
          presetSizes['Instagram Story']!,
          presetSizes['Instagram Post']!,
          presetSizes['YouTube Shorts']!,
        ];
      case ContentType.youtube:
        return [
          presetSizes['YouTube']!,
          presetSizes['YouTube Shorts']!,
          presetSizes['4K Landscape']!,
        ];
      case ContentType.professional:
        return [
          presetSizes['4K Landscape']!,
          presetSizes['4K Portrait']!,
          presetSizes['YouTube']!,
        ];
      case ContentType.custom:
      default:
        return presetSizes.values.toList();
    }
  }

  /// Calculate scale factor to fit content in output size
  static double calculateScaleFactor(Size contentSize, Size outputSize) {
    final scaleX = outputSize.width / contentSize.width;
    final scaleY = outputSize.height / contentSize.height;
    return math.min(scaleX, scaleY);
  }

  /// Calculate padding needed to center content in output size
  static EdgeInsets calculatePadding(Size contentSize, Size outputSize) {
    final scaleFactor = calculateScaleFactor(contentSize, outputSize);
    final scaledWidth = contentSize.width * scaleFactor;
    final scaledHeight = contentSize.height * scaleFactor;
    
    final horizontalPadding = (outputSize.width - scaledWidth) / 2;
    final verticalPadding = (outputSize.height - scaledHeight) / 2;
    
    return EdgeInsets.symmetric(
      horizontal: horizontalPadding,
      vertical: verticalPadding,
    );
  }

  /// Get safe area for content placement (avoiding letterboxing)
  static Rect getSafeContentArea(Size contentSize, Size outputSize) {
    final padding = calculatePadding(contentSize, outputSize);
    
    return Rect.fromLTRB(
      padding.left,
      padding.top,
      outputSize.width - padding.right,
      outputSize.height - padding.bottom,
    );
  }

  /// Convert size to different aspect ratio while maintaining area
  static Size convertToAspectRatio(Size originalSize, double targetAspectRatio) {
    final originalArea = originalSize.width * originalSize.height;
    final newHeight = math.sqrt(originalArea / targetAspectRatio);
    final newWidth = newHeight * targetAspectRatio;
    
    return Size(newWidth, newHeight);
  }

  /// Get closest preset size to a given size
  static Size getClosestPresetSize(Size targetSize) {
    Size closestSize = presetSizes.values.first;
    double minDistance = double.infinity;
    
    for (final presetSize in presetSizes.values) {
      final distance = _calculateSizeDistance(targetSize, presetSize);
      if (distance < minDistance) {
        minDistance = distance;
        closestSize = presetSize;
      }
    }
    
    return closestSize;
  }

  /// Calculate distance between two sizes
  static double _calculateSizeDistance(Size size1, Size size2) {
    final widthDiff = size1.width - size2.width;
    final heightDiff = size1.height - size2.height;
    return math.sqrt(widthDiff * widthDiff + heightDiff * heightDiff);
  }

  /// Generate FFmpeg scale filter for output size
  static String generateScaleFilter(Size outputSize, {bool maintainAspectRatio = true}) {
    final width = outputSize.width.toInt();
    final height = outputSize.height.toInt();
    
    if (maintainAspectRatio) {
      return 'scale=$width:$height:force_original_aspect_ratio=decrease,'
             'pad=$width:$height:(ow-iw)/2:(oh-ih)/2:color=black';
    } else {
      return 'scale=$width:$height';
    }
  }

  /// Validate aspect ratio compatibility
  static bool isAspectRatioCompatible(Size contentSize, Size outputSize, {double tolerance = 0.1}) {
    final contentRatio = getAspectRatio(contentSize);
    final outputRatio = getAspectRatio(outputSize);
    final difference = (contentRatio - outputRatio).abs();
    
    return difference <= tolerance;
  }

  /// Get optimal crop for aspect ratio conversion
  static Rect getOptimalCrop(Size sourceSize, Size targetSize) {
    final sourceRatio = getAspectRatio(sourceSize);
    final targetRatio = getAspectRatio(targetSize);
    
    if (sourceRatio > targetRatio) {
      // Source is wider, crop width
      final newWidth = sourceSize.height * targetRatio;
      final cropX = (sourceSize.width - newWidth) / 2;
      return Rect.fromLTWH(cropX, 0, newWidth, sourceSize.height);
    } else {
      // Source is taller, crop height
      final newHeight = sourceSize.width / targetRatio;
      final cropY = (sourceSize.height - newHeight) / 2;
      return Rect.fromLTWH(0, cropY, sourceSize.width, newHeight);
    }
  }

  /// Create a project configuration with locked output size
  static ProjectConfiguration createProjectConfiguration({
    required Size outputSize,
    required String name,
    double frameRate = 30.0,
    int bitrate = 5000000, // 5 Mbps
    String codec = 'libx264',
    String preset = 'medium',
    int crf = 23,
  }) {
    return ProjectConfiguration(
      outputSize: outputSize,
      name: name,
      aspectRatio: getAspectRatio(outputSize),
      orientation: getOrientation(outputSize),
      frameRate: frameRate,
      bitrate: bitrate,
      codec: codec,
      preset: preset,
      crf: crf,
      presetName: getPresetName(outputSize),
    );
  }
}

/// Project configuration data class
class ProjectConfiguration {
  final Size outputSize;
  final String name;
  final double aspectRatio;
  final Orientation orientation;
  final double frameRate;
  final int bitrate;
  final String codec;
  final String preset;
  final int crf;
  final String? presetName;

  const ProjectConfiguration({
    required this.outputSize,
    required this.name,
    required this.aspectRatio,
    required this.orientation,
    this.frameRate = 30.0,
    this.bitrate = 5000000,
    this.codec = 'libx264',
    this.preset = 'medium',
    this.crf = 23,
    this.presetName,
  });

  ProjectConfiguration copyWith({
    Size? outputSize,
    String? name,
    double? aspectRatio,
    Orientation? orientation,
    double? frameRate,
    int? bitrate,
    String? codec,
    String? preset,
    int? crf,
    String? presetName,
  }) {
    return ProjectConfiguration(
      outputSize: outputSize ?? this.outputSize,
      name: name ?? this.name,
      aspectRatio: aspectRatio ?? this.aspectRatio,
      orientation: orientation ?? this.orientation,
      frameRate: frameRate ?? this.frameRate,
      bitrate: bitrate ?? this.bitrate,
      codec: codec ?? this.codec,
      preset: preset ?? this.preset,
      crf: crf ?? this.crf,
      presetName: presetName ?? this.presetName,
    );
  }

  /// Generate FFmpeg output parameters
  String generateFFmpegOutputParams() {
    final width = outputSize.width.toInt();
    final height = outputSize.height.toInt();
    
    return '-c:v $codec -preset $preset -crf $crf '
           '-r $frameRate -pix_fmt yuv420p '
           '-vf "scale=$width:$height:force_original_aspect_ratio=decrease,'
           'pad=$width:$height:(ow-iw)/2:(oh-ih)/2:color=black,setsar=1" '
           '-c:a aac -b:a 128k -movflags +faststart';
  }

  @override
  String toString() {
    return 'ProjectConfiguration('
           'outputSize: $outputSize, '
           'name: $name, '
           'aspectRatio: $aspectRatio, '
           'orientation: $orientation, '
           'presetName: $presetName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProjectConfiguration &&
        other.outputSize == outputSize &&
        other.name == name &&
        other.aspectRatio == aspectRatio &&
        other.orientation == orientation &&
        other.frameRate == frameRate &&
        other.bitrate == bitrate &&
        other.codec == codec &&
        other.preset == preset &&
        other.crf == crf &&
        other.presetName == presetName;
  }

  @override
  int get hashCode {
    return outputSize.hashCode ^
        name.hashCode ^
        aspectRatio.hashCode ^
        orientation.hashCode ^
        frameRate.hashCode ^
        bitrate.hashCode ^
        codec.hashCode ^
        preset.hashCode ^
        crf.hashCode ^
        presetName.hashCode;
  }
}

/// Content types for recommended sizes
enum ContentType {
  socialMedia,
  youtube,
  professional,
  custom,
}

/// Output size selector widget
class OutputSizeSelector extends StatelessWidget {
  final Size currentSize;
  final Function(Size size) onSizeChanged;
  final ContentType contentType;

  const OutputSizeSelector({
    Key? key,
    required this.currentSize,
    required this.onSizeChanged,
    this.contentType = ContentType.socialMedia,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final recommendedSizes = ProjectOutputManager.getRecommendedSizes(contentType);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Output Size',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: recommendedSizes.map((size) {
            final isSelected = size == currentSize;
            final presetName = ProjectOutputManager.getPresetName(size);
            
            return GestureDetector(
              onTap: () => onSizeChanged(size),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected ? Colors.blue : Colors.grey.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isSelected ? Colors.blue : Colors.grey.withOpacity(0.5),
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      presetName ?? 'Custom',
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.black,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      '${size.width.toInt()}×${size.height.toInt()}',
                      style: TextStyle(
                        color: isSelected ? Colors.white70 : Colors.grey,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
}
