import 'package:ai_video_creator_editor/screens/project/models/video_track_model.dart';
import 'package:flutter/material.dart';
import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter_new/return_code.dart';
import 'package:path_provider/path_provider.dart';

/// FFmpeg crop export functionality
/// Applies crop=w:h:x:y, then scale+pad to fixed output size with setsar=1 filter
class FFmpegCropExport {
  /// Apply crop and scale operations to a video file
  static Future<String?> applyCropAndScale({
    required String inputPath,
    required CropModel cropModel,
    required Size outputSize,
    String? outputPath,
    bool maintainAspectRatio = true,
    String codec = 'libx264',
    String preset = 'medium',
    int crf = 23,
    String pixelFormat = 'yuv420p',
    Function(double progress)? onProgress,
  }) async {
    if (!cropModel.enabled || !cropModel.isValid) {
      throw ArgumentError('Invalid crop model');
    }

    final tempDir = await getTemporaryDirectory();
    final finalOutputPath = outputPath ??
        '${tempDir.path}/cropped_${DateTime.now().millisecondsSinceEpoch}.mp4';

    try {
      // Build the filter complex
      final filterComplex = _buildCropScaleFilter(
        cropModel: cropModel,
        outputSize: outputSize,
        maintainAspectRatio: maintainAspectRatio,
      );

      // Build the complete FFmpeg command
      final command = _buildFFmpegCommand(
        inputPath: inputPath,
        outputPath: finalOutputPath,
        filterComplex: filterComplex,
        codec: codec,
        preset: preset,
        crf: crf,
        pixelFormat: pixelFormat,
      );

      print('Executing FFmpeg crop command: $command');

      // Execute the command
      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();

      if (ReturnCode.isSuccess(returnCode)) {
        print('Crop and scale operation completed successfully');
        return finalOutputPath;
      } else {
        final logs = await session.getOutput();
        print('FFmpeg crop operation failed: $logs');
        throw Exception('FFmpeg crop operation failed: $logs');
      }
    } catch (e) {
      print('Error in crop and scale operation: $e');
      rethrow;
    }
  }

  /// Build the crop and scale filter complex
  static String _buildCropScaleFilter({
    required CropModel cropModel,
    required Size outputSize,
    bool maintainAspectRatio = true,
  }) {
    final filters = <String>[];

    // Step 1: Apply crop
    final cropFilter = cropModel.toFFmpegFilter();
    if (cropFilter.isNotEmpty) {
      filters.add(cropFilter);
    }

    // Step 2: Scale and pad to output size
    final scaleFilter = _buildScaleFilter(
      targetWidth: outputSize.width.toInt(),
      targetHeight: outputSize.height.toInt(),
      maintainAspectRatio: maintainAspectRatio,
    );
    filters.add(scaleFilter);

    // Step 3: Set sample aspect ratio to 1:1
    filters.add('setsar=1');

    return '[0:v]${filters.join(',')}[vout]';
  }

  /// Build scale filter with padding
  static String _buildScaleFilter({
    required int targetWidth,
    required int targetHeight,
    bool maintainAspectRatio = true,
  }) {
    if (maintainAspectRatio) {
      // Scale to fit within target dimensions, then pad
      return 'scale=$targetWidth:$targetHeight:force_original_aspect_ratio=decrease,'
          'pad=$targetWidth:$targetHeight:(ow-iw)/2:(oh-ih)/2:color=black';
    } else {
      // Scale to exact dimensions (may distort)
      return 'scale=$targetWidth:$targetHeight';
    }
  }

  /// Build complete FFmpeg command
  static String _buildFFmpegCommand({
    required String inputPath,
    required String outputPath,
    required String filterComplex,
    required String codec,
    required String preset,
    required int crf,
    required String pixelFormat,
  }) {
    return '-y -i "$inputPath" '
        '-filter_complex "$filterComplex" '
        '-map "[vout]" -map 0:a? '
        '-c:v $codec -preset $preset -crf $crf '
        '-c:a aac -b:a 128k '
        '-pix_fmt $pixelFormat '
        '-movflags +faststart '
        '"$outputPath"';
  }

  /// Apply crop to multiple video segments
  static Future<List<String>> applyCropToSegments({
    required List<VideoTrackModel> videoTracks,
    required Size outputSize,
    bool maintainAspectRatio = true,
    Function(int current, int total)? onProgress,
  }) async {
    final processedPaths = <String>[];

    for (int i = 0; i < videoTracks.length; i++) {
      final track = videoTracks[i];

      onProgress?.call(i, videoTracks.length);

      if (track.cropModel != null && track.cropModel!.enabled) {
        try {
          final croppedPath = await applyCropAndScale(
            inputPath: track.processedFile.path,
            cropModel: track.cropModel!,
            outputSize: outputSize,
            maintainAspectRatio: maintainAspectRatio,
          );

          if (croppedPath != null) {
            processedPaths.add(croppedPath);
          } else {
            // Fallback to original if crop fails
            processedPaths.add(track.processedFile.path);
          }
        } catch (e) {
          print('Failed to crop segment ${track.id}: $e');
          // Fallback to original if crop fails
          processedPaths.add(track.processedFile.path);
        }
      } else {
        // No crop needed, use original
        processedPaths.add(track.processedFile.path);
      }
    }

    onProgress?.call(videoTracks.length, videoTracks.length);
    return processedPaths;
  }

  /// Validate crop parameters against video dimensions
  static Future<bool> validateCropForVideo({
    required String videoPath,
    required CropModel cropModel,
  }) async {
    try {
      // Get video dimensions using FFprobe
      final dimensions = await getVideoDimensions(videoPath);
      if (dimensions == null) return false;

      // Update crop model with actual video dimensions
      final updatedCropModel = cropModel.copyWith(
        sourceWidth: dimensions.width,
        sourceHeight: dimensions.height,
      );

      return updatedCropModel.isValid;
    } catch (e) {
      print('Error validating crop: $e');
      return false;
    }
  }

  /// Get video dimensions using FFprobe
  static Future<Size?> getVideoDimensions(String videoPath) async {
    try {
      final command = '-v quiet -print_format json -show_streams "$videoPath"';
      final session = await FFmpegKit.execute('-f ffprobe $command');
      final output = await session.getOutput();

      if (output != null) {
        // Parse JSON output to extract video dimensions
        // This is a simplified version - in production, use proper JSON parsing
        final widthMatch = RegExp(r'"width":\s*(\d+)').firstMatch(output);
        final heightMatch = RegExp(r'"height":\s*(\d+)').firstMatch(output);

        if (widthMatch != null && heightMatch != null) {
          final width = double.parse(widthMatch.group(1)!);
          final height = double.parse(heightMatch.group(1)!);
          return Size(width, height);
        }
      }
    } catch (e) {
      print('Error getting video dimensions: $e');
    }

    return null;
  }

  /// Create a crop model that maintains a specific aspect ratio
  static CropModel createAspectRatioCrop({
    required Size videoSize,
    required double targetAspectRatio,
    CropAlignment alignment = CropAlignment.center,
  }) {
    final videoAspectRatio = videoSize.width / videoSize.height;

    double cropWidth, cropHeight, cropX, cropY;

    if (videoAspectRatio > targetAspectRatio) {
      // Video is wider than target, crop width
      cropHeight = videoSize.height;
      cropWidth = cropHeight * targetAspectRatio;
      cropY = 0;

      switch (alignment) {
        case CropAlignment.left:
          cropX = 0;
          break;
        case CropAlignment.right:
          cropX = videoSize.width - cropWidth;
          break;
        case CropAlignment.center:
        default:
          cropX = (videoSize.width - cropWidth) / 2;
          break;
      }
    } else {
      // Video is taller than target, crop height
      cropWidth = videoSize.width;
      cropHeight = cropWidth / targetAspectRatio;
      cropX = 0;

      switch (alignment) {
        case CropAlignment.top:
          cropY = 0;
          break;
        case CropAlignment.bottom:
          cropY = videoSize.height - cropHeight;
          break;
        case CropAlignment.center:
        default:
          cropY = (videoSize.height - cropHeight) / 2;
          break;
      }
    }

    return CropModel(
      x: cropX,
      y: cropY,
      width: cropWidth,
      height: cropHeight,
      sourceWidth: videoSize.width,
      sourceHeight: videoSize.height,
      enabled: true,
    );
  }

  /// Batch process multiple videos with the same crop settings
  static Future<List<String>> batchCropVideos({
    required List<String> inputPaths,
    required CropModel cropModel,
    required Size outputSize,
    bool maintainAspectRatio = true,
    Function(int current, int total, String currentFile)? onProgress,
  }) async {
    final processedPaths = <String>[];

    for (int i = 0; i < inputPaths.length; i++) {
      final inputPath = inputPaths[i];

      onProgress?.call(i, inputPaths.length, inputPath);

      try {
        final processedPath = await applyCropAndScale(
          inputPath: inputPath,
          cropModel: cropModel,
          outputSize: outputSize,
          maintainAspectRatio: maintainAspectRatio,
        );

        if (processedPath != null) {
          processedPaths.add(processedPath);
        }
      } catch (e) {
        print('Failed to process $inputPath: $e');
        // Continue with next file
      }
    }

    onProgress?.call(inputPaths.length, inputPaths.length, '');
    return processedPaths;
  }

  /// Preview crop by generating a thumbnail
  static Future<String?> generateCropPreview({
    required String videoPath,
    required CropModel cropModel,
    double timeOffset = 5.0,
    Size? thumbnailSize,
  }) async {
    final tempDir = await getTemporaryDirectory();
    final previewPath =
        '${tempDir.path}/crop_preview_${DateTime.now().millisecondsSinceEpoch}.jpg';

    try {
      final cropFilter = cropModel.toFFmpegFilter();
      final scaleFilter = thumbnailSize != null
          ? ',scale=${thumbnailSize.width.toInt()}:${thumbnailSize.height.toInt()}'
          : '';

      final command = '-ss $timeOffset -i "$videoPath" '
          '-vf "$cropFilter$scaleFilter" '
          '-vframes 1 -q:v 2 '
          '"$previewPath"';

      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();

      if (ReturnCode.isSuccess(returnCode)) {
        return previewPath;
      }
    } catch (e) {
      print('Error generating crop preview: $e');
    }

    return null;
  }
}

/// Crop alignment options
enum CropAlignment {
  center,
  top,
  bottom,
  left,
  right,
  topLeft,
  topRight,
  bottomLeft,
  bottomRight,
}
