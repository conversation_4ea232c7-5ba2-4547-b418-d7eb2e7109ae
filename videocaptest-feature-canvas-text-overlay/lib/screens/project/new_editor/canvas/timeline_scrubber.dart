import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/video_editor_provider.dart';
import 'dart:math' as math;

/// Timeline scrubbing functionality with smooth performance
/// Allows moving playhead and updating preview in real-time
class TimelineScrubber extends StatefulWidget {
  final double height;
  final double pixelsPerSecond;
  final Function(double time)? onTimeChanged;
  final Function(double time)? onScrubStart;
  final Function(double time)? onScrubEnd;
  final bool showThumbnails;
  final bool showWaveform;

  const TimelineScrubber({
    Key? key,
    this.height = 80.0,
    this.pixelsPerSecond = 50.0,
    this.onTimeChanged,
    this.onScrubStart,
    this.onScrubEnd,
    this.showThumbnails = true,
    this.showWaveform = true,
  }) : super(key: key);

  @override
  State<TimelineScrubber> createState() => _TimelineScrubberState();
}

class _TimelineScrubberState extends State<TimelineScrubber>
    with TickerProviderStateMixin {
  late ScrollController _scrollController;
  late AnimationController _playheadAnimationController;
  late Animation<double> _playheadAnimation;
  
  bool _isScrubbing = false;
  bool _wasPlayingBeforeScrub = false;
  double _scrubStartTime = 0.0;
  Offset? _lastPanPosition;
  
  // Performance optimization
  DateTime _lastUpdateTime = DateTime.now();
  static const Duration _updateThrottle = Duration(milliseconds: 16); // 60fps

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _playheadAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _playheadAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _playheadAnimationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _playheadAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<VideoEditorProvider>(
      builder: (context, provider, child) {
        final totalWidth = provider.videoDuration * widget.pixelsPerSecond;
        
        return Container(
          height: widget.height,
          decoration: BoxDecoration(
            color: const Color(0xFF2A2A2A),
            border: Border.all(color: Colors.grey.withOpacity(0.3)),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Stack(
            children: [
              // Background timeline
              _buildTimelineBackground(provider, totalWidth),
              
              // Thumbnail strip (if enabled)
              if (widget.showThumbnails)
                _buildThumbnailStrip(provider, totalWidth),
              
              // Waveform (if enabled)
              if (widget.showWaveform)
                _buildWaveformStrip(provider, totalWidth),
              
              // Time markers
              _buildTimeMarkers(provider, totalWidth),
              
              // Playhead
              _buildPlayhead(provider, totalWidth),
              
              // Scrub overlay
              _buildScrubOverlay(provider, totalWidth),
            ],
          ),
        );
      },
    );
  }

  /// Build timeline background
  Widget _buildTimelineBackground(VideoEditorProvider provider, double totalWidth) {
    return Positioned.fill(
      child: SingleChildScrollView(
        controller: _scrollController,
        scrollDirection: Axis.horizontal,
        child: Container(
          width: totalWidth,
          height: widget.height,
          color: const Color(0xFF1E1E1E),
        ),
      ),
    );
  }

  /// Build thumbnail strip
  Widget _buildThumbnailStrip(VideoEditorProvider provider, double totalWidth) {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      height: widget.height - 20, // Leave space for time markers
      child: SingleChildScrollView(
        controller: _scrollController,
        scrollDirection: Axis.horizontal,
        child: Container(
          width: totalWidth,
          height: widget.height - 20,
          child: CustomPaint(
            painter: ThumbnailStripPainter(
              framePaths: provider.framePaths,
              totalDuration: provider.videoDuration,
              pixelsPerSecond: widget.pixelsPerSecond,
            ),
          ),
        ),
      ),
    );
  }

  /// Build waveform strip
  Widget _buildWaveformStrip(VideoEditorProvider provider, double totalWidth) {
    return Positioned(
      bottom: 20,
      left: 0,
      right: 0,
      height: 30,
      child: SingleChildScrollView(
        controller: _scrollController,
        scrollDirection: Axis.horizontal,
        child: Container(
          width: totalWidth,
          height: 30,
          child: CustomPaint(
            painter: WaveformStripPainter(
              waveformData: provider.waveformData,
              totalDuration: provider.videoDuration,
              pixelsPerSecond: widget.pixelsPerSecond,
              currentTime: provider.videoPosition,
            ),
          ),
        ),
      ),
    );
  }

  /// Build time markers
  Widget _buildTimeMarkers(VideoEditorProvider provider, double totalWidth) {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      height: 20,
      child: SingleChildScrollView(
        controller: _scrollController,
        scrollDirection: Axis.horizontal,
        child: Container(
          width: totalWidth,
          height: 20,
          child: CustomPaint(
            painter: TimeMarkerPainter(
              totalDuration: provider.videoDuration,
              pixelsPerSecond: widget.pixelsPerSecond,
            ),
          ),
        ),
      ),
    );
  }

  /// Build playhead
  Widget _buildPlayhead(VideoEditorProvider provider, double totalWidth) {
    final playheadX = provider.videoPosition * widget.pixelsPerSecond;
    
    return Positioned(
      left: playheadX - 1,
      top: 0,
      child: AnimatedBuilder(
        animation: _playheadAnimation,
        builder: (context, child) {
          return Container(
            width: 2,
            height: widget.height,
            decoration: BoxDecoration(
              color: _isScrubbing 
                  ? Colors.yellow 
                  : Colors.red.withOpacity(0.8 + 0.2 * _playheadAnimation.value),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 2,
                  offset: const Offset(1, 0),
                ),
              ],
            ),
            child: Column(
              children: [
                // Playhead handle
                Container(
                  width: 12,
                  height: 12,
                  margin: const EdgeInsets.only(left: -5),
                  decoration: BoxDecoration(
                    color: _isScrubbing ? Colors.yellow : Colors.red,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 1),
                  ),
                ),
                // Playhead line
                Expanded(
                  child: Container(
                    width: 2,
                    color: _isScrubbing ? Colors.yellow : Colors.red,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// Build scrub overlay for gesture detection
  Widget _buildScrubOverlay(VideoEditorProvider provider, double totalWidth) {
    return Positioned.fill(
      child: SingleChildScrollView(
        controller: _scrollController,
        scrollDirection: Axis.horizontal,
        child: GestureDetector(
          onTapDown: (details) => _handleTapDown(details, provider),
          onPanStart: (details) => _handlePanStart(details, provider),
          onPanUpdate: (details) => _handlePanUpdate(details, provider),
          onPanEnd: (details) => _handlePanEnd(details, provider),
          child: Container(
            width: totalWidth,
            height: widget.height,
            color: Colors.transparent,
          ),
        ),
      ),
    );
  }

  /// Handle tap down for seeking
  void _handleTapDown(TapDownDetails details, VideoEditorProvider provider) {
    final time = details.localPosition.dx / widget.pixelsPerSecond;
    final clampedTime = time.clamp(0.0, provider.videoDuration);
    
    provider.seekTo(clampedTime);
    widget.onTimeChanged?.call(clampedTime);
    
    // Animate playhead
    _playheadAnimationController.forward().then((_) {
      _playheadAnimationController.reverse();
    });
  }

  /// Handle pan start for scrubbing
  void _handlePanStart(DragStartDetails details, VideoEditorProvider provider) {
    setState(() {
      _isScrubbing = true;
      _wasPlayingBeforeScrub = provider.isPlaying;
      _scrubStartTime = provider.videoPosition;
      _lastPanPosition = details.localPosition;
    });
    
    // Pause video during scrubbing
    if (_wasPlayingBeforeScrub) {
      provider.togglePlay();
    }
    
    widget.onScrubStart?.call(_scrubStartTime);
  }

  /// Handle pan update for scrubbing
  void _handlePanUpdate(DragUpdateDetails details, VideoEditorProvider provider) {
    if (!_isScrubbing) return;
    
    // Throttle updates for performance
    final now = DateTime.now();
    if (now.difference(_lastUpdateTime) < _updateThrottle) {
      return;
    }
    _lastUpdateTime = now;
    
    final time = details.localPosition.dx / widget.pixelsPerSecond;
    final clampedTime = time.clamp(0.0, provider.videoDuration);
    
    provider.seekTo(clampedTime);
    widget.onTimeChanged?.call(clampedTime);
    
    _lastPanPosition = details.localPosition;
  }

  /// Handle pan end for scrubbing
  void _handlePanEnd(DragEndDetails details, VideoEditorProvider provider) {
    final finalTime = provider.videoPosition;
    
    setState(() {
      _isScrubbing = false;
    });
    
    // Resume playback if it was playing before
    if (_wasPlayingBeforeScrub) {
      provider.togglePlay();
    }
    
    widget.onScrubEnd?.call(finalTime);
    
    // Auto-scroll to keep playhead visible
    _autoScrollToPlayhead(provider);
  }

  /// Auto-scroll to keep playhead visible
  void _autoScrollToPlayhead(VideoEditorProvider provider) {
    final playheadX = provider.videoPosition * widget.pixelsPerSecond;
    final viewportWidth = MediaQuery.of(context).size.width;
    final currentScrollOffset = _scrollController.offset;
    
    // Check if playhead is outside visible area
    if (playheadX < currentScrollOffset || 
        playheadX > currentScrollOffset + viewportWidth) {
      
      final targetScrollOffset = (playheadX - viewportWidth / 2)
          .clamp(0.0, _scrollController.position.maxScrollExtent);
      
      _scrollController.animateTo(
        targetScrollOffset,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }
}

/// Custom painter for thumbnail strip
class ThumbnailStripPainter extends CustomPainter {
  final List<String> framePaths;
  final double totalDuration;
  final double pixelsPerSecond;

  ThumbnailStripPainter({
    required this.framePaths,
    required this.totalDuration,
    required this.pixelsPerSecond,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (framePaths.isEmpty) return;
    
    final thumbnailWidth = size.width / framePaths.length;
    
    for (int i = 0; i < framePaths.length; i++) {
      final rect = Rect.fromLTWH(
        i * thumbnailWidth,
        0,
        thumbnailWidth,
        size.height,
      );
      
      // Draw placeholder for thumbnail
      final paint = Paint()
        ..color = Colors.grey.withOpacity(0.3)
        ..style = PaintingStyle.fill;
      
      canvas.drawRect(rect, paint);
      
      // Draw border
      final borderPaint = Paint()
        ..color = Colors.grey.withOpacity(0.5)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1;
      
      canvas.drawRect(rect, borderPaint);
    }
  }

  @override
  bool shouldRepaint(ThumbnailStripPainter oldDelegate) {
    return oldDelegate.framePaths != framePaths ||
        oldDelegate.totalDuration != totalDuration ||
        oldDelegate.pixelsPerSecond != pixelsPerSecond;
  }
}

/// Custom painter for waveform strip
class WaveformStripPainter extends CustomPainter {
  final List<double> waveformData;
  final double totalDuration;
  final double pixelsPerSecond;
  final double currentTime;

  WaveformStripPainter({
    required this.waveformData,
    required this.totalDuration,
    required this.pixelsPerSecond,
    required this.currentTime,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (waveformData.isEmpty) return;
    
    final paint = Paint()
      ..strokeWidth = 1
      ..strokeCap = StrokeCap.round;
    
    final centerY = size.height / 2;
    final pointSpacing = size.width / waveformData.length;
    final currentX = currentTime * pixelsPerSecond;
    
    for (int i = 0; i < waveformData.length - 1; i++) {
      final x = i * pointSpacing;
      final nextX = (i + 1) * pointSpacing;
      
      // Color based on playback position
      paint.color = x < currentX ? Colors.green : Colors.grey;
      
      final amplitude = waveformData[i] * (size.height * 0.4);
      final nextAmplitude = waveformData[i + 1] * (size.height * 0.4);
      
      canvas.drawLine(
        Offset(x, centerY - amplitude),
        Offset(nextX, centerY - nextAmplitude),
        paint,
      );
      
      canvas.drawLine(
        Offset(x, centerY + amplitude),
        Offset(nextX, centerY + nextAmplitude),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(WaveformStripPainter oldDelegate) {
    return oldDelegate.waveformData != waveformData ||
        oldDelegate.currentTime != currentTime ||
        oldDelegate.totalDuration != totalDuration ||
        oldDelegate.pixelsPerSecond != pixelsPerSecond;
  }
}

/// Custom painter for time markers
class TimeMarkerPainter extends CustomPainter {
  final double totalDuration;
  final double pixelsPerSecond;

  TimeMarkerPainter({
    required this.totalDuration,
    required this.pixelsPerSecond,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey.withOpacity(0.6)
      ..strokeWidth = 1;
    
    final textPainter = TextPainter(textDirection: TextDirection.ltr);
    
    // Draw markers every second
    for (double time = 0; time <= totalDuration; time += 1.0) {
      final x = time * pixelsPerSecond;
      
      // Draw marker line
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
      
      // Draw time label every 5 seconds
      if (time % 5.0 == 0) {
        final minutes = (time / 60).floor();
        final seconds = (time % 60).floor();
        final timeText = '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
        
        textPainter.text = TextSpan(
          text: timeText,
          style: const TextStyle(
            color: Colors.grey,
            fontSize: 8,
          ),
        );
        textPainter.layout();
        
        if (x + textPainter.width / 2 < size.width) {
          textPainter.paint(canvas, Offset(x - textPainter.width / 2, 2));
        }
      }
    }
  }

  @override
  bool shouldRepaint(TimeMarkerPainter oldDelegate) {
    return oldDelegate.totalDuration != totalDuration ||
        oldDelegate.pixelsPerSecond != pixelsPerSecond;
  }
}
