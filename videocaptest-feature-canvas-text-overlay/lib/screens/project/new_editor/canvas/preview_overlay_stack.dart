import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/video_editor_provider.dart';
import 'package:ai_video_creator_editor/screens/project/models/text_track_model.dart';
import 'package:ai_video_creator_editor/screens/project/models/overlay_video_track_model.dart';
import 'overlay_item_widget.dart';
import 'dart:math' as math;

/// Preview Overlay Stack with base video texture and overlay layers
/// Handles text, images, stickers, and shapes using Stack with Positioned + Transform
class PreviewOverlayStack extends StatefulWidget {
  final Widget videoPreview;
  final Size containerSize;
  final VoidCallback? onOverlayChanged;

  const PreviewOverlayStack({
    Key? key,
    required this.videoPreview,
    required this.containerSize,
    this.onOverlayChanged,
  }) : super(key: key);

  @override
  State<PreviewOverlayStack> createState() => _PreviewOverlayStackState();
}

class _PreviewOverlayStackState extends State<PreviewOverlayStack> {
  String? _selectedOverlayId;
  bool _isDragging = false;
  bool _isRotating = false;
  bool _isScaling = false;
  
  // Transform state for active overlay
  Offset _dragOffset = Offset.zero;
  double _rotationAngle = 0.0;
  double _scaleValue = 1.0;
  Offset _initialFocalPoint = Offset.zero;

  @override
  Widget build(BuildContext context) {
    return Consumer<VideoEditorProvider>(
      builder: (context, provider, child) {
        return Container(
          width: widget.containerSize.width,
          height: widget.containerSize.height,
          child: Stack(
            clipBehavior: Clip.hardEdge,
            children: [
              // Base video texture
              Positioned.fill(child: widget.videoPreview),
              
              // Text overlays
              ..._buildTextOverlays(provider),
              
              // Image/Sticker overlays
              ..._buildImageOverlays(provider),
              
              // Shape overlays
              ..._buildShapeOverlays(provider),
              
              // Video overlays
              ..._buildVideoOverlays(provider),
              
              // Selection handles for active overlay
              if (_selectedOverlayId != null)
                _buildSelectionHandles(provider),
            ],
          ),
        );
      },
    );
  }

  /// Build text overlay widgets
  List<Widget> _buildTextOverlays(VideoEditorProvider provider) {
    final currentTime = provider.videoPosition;
    final visibleTextTracks = provider.textTracks.where((track) =>
        currentTime >= track.trimStartTime && currentTime <= track.trimEndTime);

    return visibleTextTracks.map((track) {
      final isSelected = _selectedOverlayId == track.id;
      
      return Positioned(
        left: track.position.dx,
        top: track.position.dy,
        child: GestureDetector(
          onTap: () => _selectOverlay(track.id),
          onPanStart: isSelected ? _handlePanStart : null,
          onPanUpdate: isSelected ? (details) => _handleTextDrag(details, track, provider) : null,
          onPanEnd: isSelected ? _handlePanEnd : null,
          child: Transform.rotate(
            angle: track.rotation * math.pi / 180,
            child: OverlayItemWidget(
              type: OverlayType.text,
              content: track.text,
              style: TextStyle(
                color: track.textColor,
                fontSize: track.fontSize,
                fontFamily: track.fontFamily,
              ),
              isSelected: isSelected,
              opacity: 1.0,
            ),
          ),
        ),
      );
    }).toList();
  }

  /// Build image/sticker overlay widgets
  List<Widget> _buildImageOverlays(VideoEditorProvider provider) {
    // This will be implemented when image overlay models are added
    return [];
  }

  /// Build shape overlay widgets
  List<Widget> _buildShapeOverlays(VideoEditorProvider provider) {
    // This will be implemented when shape overlay models are added
    return [];
  }

  /// Build video overlay widgets
  List<Widget> _buildVideoOverlays(VideoEditorProvider provider) {
    final currentTime = provider.videoPosition;
    final visibleOverlayTracks = provider.overlayVideoTracks.where((track) =>
        currentTime >= track.trimStartTime && currentTime <= track.trimEndTime);

    return visibleOverlayTracks.map((track) {
      final isSelected = _selectedOverlayId == track.id;
      
      return Positioned(
        left: track.position?.left ?? 0,
        top: track.position?.top ?? 0,
        width: track.position?.width ?? widget.containerSize.width,
        height: track.position?.height ?? widget.containerSize.height,
        child: GestureDetector(
          onTap: () => _selectOverlay(track.id),
          onPanStart: isSelected ? _handlePanStart : null,
          onPanUpdate: isSelected ? (details) => _handleVideoDrag(details, track, provider) : null,
          onPanEnd: isSelected ? _handlePanEnd : null,
          child: Transform.scale(
            scale: isSelected ? _scaleValue : 1.0,
            child: Opacity(
              opacity: track.opacity,
              child: OverlayItemWidget(
                type: OverlayType.video,
                content: track.videoFile.path,
                isSelected: isSelected,
                opacity: track.opacity,
                blendMode: _getBlendMode(track.blendMode),
              ),
            ),
          ),
        ),
      );
    }).toList();
  }

  /// Build selection handles for the active overlay
  Widget _buildSelectionHandles(VideoEditorProvider provider) {
    // Find the selected overlay bounds
    Rect? overlayBounds = _getSelectedOverlayBounds(provider);
    
    if (overlayBounds == null) return const SizedBox.shrink();
    
    return Positioned(
      left: overlayBounds.left - 8,
      top: overlayBounds.top - 8,
      width: overlayBounds.width + 16,
      height: overlayBounds.height + 16,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.blue, width: 2),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Stack(
          children: [
            // Corner handles for scaling
            _buildCornerHandle(Alignment.topLeft, _handleScaleStart),
            _buildCornerHandle(Alignment.topRight, _handleScaleStart),
            _buildCornerHandle(Alignment.bottomLeft, _handleScaleStart),
            _buildCornerHandle(Alignment.bottomRight, _handleScaleStart),
            
            // Rotation handle
            Positioned(
              top: -20,
              left: overlayBounds.width / 2 - 8,
              child: GestureDetector(
                onPanStart: _handleRotationStart,
                onPanUpdate: _handleRotationUpdate,
                onPanEnd: _handleRotationEnd,
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.rotate_right,
                    size: 12,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build corner handle for scaling
  Widget _buildCornerHandle(Alignment alignment, VoidCallback onStart) {
    return Align(
      alignment: alignment,
      child: GestureDetector(
        onPanStart: (_) => onStart(),
        onPanUpdate: _handleScaleUpdate,
        onPanEnd: _handleScaleEnd,
        child: Container(
          width: 16,
          height: 16,
          decoration: const BoxDecoration(
            color: Colors.blue,
            shape: BoxShape.circle,
          ),
        ),
      ),
    );
  }

  /// Select an overlay
  void _selectOverlay(String overlayId) {
    setState(() {
      _selectedOverlayId = overlayId;
    });
  }

  /// Handle pan start for dragging
  void _handlePanStart(DragStartDetails details) {
    setState(() {
      _isDragging = true;
      _dragOffset = Offset.zero;
    });
  }

  /// Handle text overlay dragging
  void _handleTextDrag(DragUpdateDetails details, TextTrackModel track, VideoEditorProvider provider) {
    if (!_isDragging) return;
    
    final newPosition = Offset(
      track.position.dx + details.delta.dx,
      track.position.dy + details.delta.dy,
    );
    
    // Clamp position within container bounds
    final clampedPosition = Offset(
      newPosition.dx.clamp(0, widget.containerSize.width - 100), // Approximate text width
      newPosition.dy.clamp(0, widget.containerSize.height - 50), // Approximate text height
    );
    
    final updatedTrack = track.copyWith(position: clampedPosition);
    final index = provider.textTracks.indexOf(track);
    provider.updateTextTrackModelByIndex(index, updatedTrack);
  }

  /// Handle video overlay dragging
  void _handleVideoDrag(DragUpdateDetails details, OverlayVideoTrackModel track, VideoEditorProvider provider) {
    if (!_isDragging) return;
    
    final currentPosition = track.position ?? Rect.zero;
    final newPosition = Rect.fromLTWH(
      currentPosition.left + details.delta.dx,
      currentPosition.top + details.delta.dy,
      currentPosition.width,
      currentPosition.height,
    );
    
    // Clamp position within container bounds
    final clampedPosition = Rect.fromLTWH(
      newPosition.left.clamp(0, widget.containerSize.width - newPosition.width),
      newPosition.top.clamp(0, widget.containerSize.height - newPosition.height),
      newPosition.width,
      newPosition.height,
    );
    
    final updatedTrack = track.copyWith(position: clampedPosition);
    final index = provider.overlayVideoTracks.indexOf(track);
    provider.updateOverlayTrack(index, updatedTrack);
  }

  /// Handle pan end
  void _handlePanEnd(DragEndDetails details) {
    setState(() {
      _isDragging = false;
      _dragOffset = Offset.zero;
    });
    widget.onOverlayChanged?.call();
  }

  /// Handle scale start
  void _handleScaleStart() {
    setState(() {
      _isScaling = true;
      _scaleValue = 1.0;
    });
  }

  /// Handle scale update
  void _handleScaleUpdate(DragUpdateDetails details) {
    if (!_isScaling) return;
    
    // Simple scaling based on drag distance
    final scaleDelta = details.delta.dx * 0.01;
    setState(() {
      _scaleValue = (_scaleValue + scaleDelta).clamp(0.1, 3.0);
    });
  }

  /// Handle scale end
  void _handleScaleEnd(DragEndDetails details) {
    setState(() {
      _isScaling = false;
    });
    widget.onOverlayChanged?.call();
  }

  /// Handle rotation start
  void _handleRotationStart(DragStartDetails details) {
    setState(() {
      _isRotating = true;
      _initialFocalPoint = details.localPosition;
      _rotationAngle = 0.0;
    });
  }

  /// Handle rotation update
  void _handleRotationUpdate(DragUpdateDetails details) {
    if (!_isRotating) return;
    
    // Calculate rotation angle based on drag
    final center = Offset(widget.containerSize.width / 2, widget.containerSize.height / 2);
    final angle = math.atan2(
      details.localPosition.dy - center.dy,
      details.localPosition.dx - center.dx,
    );
    
    setState(() {
      _rotationAngle = angle;
    });
  }

  /// Handle rotation end
  void _handleRotationEnd(DragEndDetails details) {
    setState(() {
      _isRotating = false;
    });
    widget.onOverlayChanged?.call();
  }

  /// Get bounds of the selected overlay
  Rect? _getSelectedOverlayBounds(VideoEditorProvider provider) {
    if (_selectedOverlayId == null) return null;
    
    // Find text track
    try {
      final textTrack = provider.textTracks.firstWhere((t) => t.id == _selectedOverlayId);
      return Rect.fromLTWH(
        textTrack.position.dx,
        textTrack.position.dy,
        100, // Approximate text width
        50,  // Approximate text height
      );
    } catch (e) {
      // Not a text track
    }
    
    // Find overlay video track
    try {
      final overlayTrack = provider.overlayVideoTracks.firstWhere((t) => t.id == _selectedOverlayId);
      return overlayTrack.position ?? Rect.fromLTWH(0, 0, widget.containerSize.width, widget.containerSize.height);
    } catch (e) {
      // Not an overlay video track
    }
    
    return null;
  }

  /// Convert blend mode string to BlendMode
  BlendMode _getBlendMode(String blendModeString) {
    switch (blendModeString.toLowerCase()) {
      case 'multiply':
        return BlendMode.multiply;
      case 'screen':
        return BlendMode.screen;
      case 'overlay':
        return BlendMode.overlay;
      case 'darken':
        return BlendMode.darken;
      case 'lighten':
        return BlendMode.lighten;
      default:
        return BlendMode.srcOver;
    }
  }
}
