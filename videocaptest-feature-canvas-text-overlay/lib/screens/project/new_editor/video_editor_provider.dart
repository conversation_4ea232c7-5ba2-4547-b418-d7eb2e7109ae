import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:ai_video_creator_editor/controllers/video_controller.dart';
import 'package:ai_video_creator_editor/screens/project/editor_controller.dart';
import 'package:ai_video_creator_editor/screens/project/models/audio_track_model.dart';
import 'package:ai_video_creator_editor/screens/project/models/text_track_model.dart';
import 'package:ai_video_creator_editor/screens/project/models/video_track_model.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/audio_trimmer.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/text_overlay_manager.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/transition_picker.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/video_export_manager.dart';
import 'package:ai_video_creator_editor/utils/snack_bar_utils.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter_new/ffmpeg_session.dart';
import 'package:ffmpeg_kit_flutter_new/return_code.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:linked_scroll_controller/linked_scroll_controller.dart';
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';

import '../../../utils/functions.dart';
import 'caption_editor.dart';
import 'frame_extractor.dart';
import 'package:ai_video_creator_editor/screens/project/models/overlay_video_track_model.dart';

class VideoEditorProvider with ChangeNotifier {
  // Controllers
  VideoEditorController? _videoEditorController;
  LinkedScrollControllerGroup _linkedScrollControllerGroup =
      LinkedScrollControllerGroup();
  ScrollController? _videoScrollController;
  ScrollController? _audioScrollController;
  ScrollController? _textScrollController;
  TextEditingController _textEditingController = TextEditingController();
  AudioPlayer? _audioController;

  // Basic properties
  bool _isInitializingVideo = false;
  String? _selectedAudio;
  List<String> _assets = [];
  List<TextOverlay> _textOverlays = [];
  String _currentFilter = 'none';
  String? _currentVideoPath;
  EditMode _editMode = EditMode.none;
  Duration? _audioDuration;
  List<VideoCaption> _captions = [];
  List<VideoTrackModel> _videoTracks = [];
  List<AudioTrackModel> _audioTracks = [];
  List<TextTrackModel> _textTracks = [];
  int _selectedVideoTrackIndex = -1;
  int _selectedAudioTrackIndex = -1;
  int _selectedTextTrackIndex = -1;

  // Trim and crop properties
  double _trimStart = 0.0;
  double _trimEnd = 0.0;
  double _audioTrimStart = 0.0;
  double _audioTrimEnd = 0.0;
  Rect? _cropRect;
  Rect?
      _appliedCropRect; // Store the crop that was actually applied for text overlay positioning

  // Video manipulation properties
  int _rotation = 0;
  TransitionType _selectedTransition = TransitionType.none;
  double _playbackSpeed = 1.0;
  double _videoVolume = 1.0;
  double _audioVolume = 1.0;

  // UI state properties
  bool loading = false;
  bool _isPlaying = false;
  bool _isExtractingFrames = false;
  List<String> _framePaths = [];
  List<double> _waveformData = [];
  bool _textFieldVisibility = false;
  bool _sendButtonVisibility = false;
  String _layeredTextOnVideo = '';
  double _videoPosition = 0.0;
  double _videoDuration = 0.0;
  Color selectedTrackBorderColor = Colors.white;
  double? _previewHeight;

  // Canvas-based text overlay properties
  bool _useCanvasForTextOverlays = true;
  Size? _previewContainerSize;

  // Undo/Redo stacks
  final List<EditOperation> _undoStack = [];
  final List<EditOperation> _redoStack = [];

  // Overlay video tracks
  final List<OverlayVideoTrackModel> _overlayVideoTracks = [];
  List<OverlayVideoTrackModel> get overlayVideoTracks => _overlayVideoTracks;

  // Mute state for overlay videos
  final Map<String, bool> videoMuteStates = {};
  void toggleVideoMute(String videoId) {
    videoMuteStates[videoId] = !(videoMuteStates[videoId] ?? false);
    if (_videoTracks.isNotEmpty &&
        _videoTracks[_selectedVideoTrackIndex].id == videoId) {
      _videoEditorController?.video.setVolume(
        videoMuteStates[videoId]! ? 0.0 : 1.0,
      );
    }
    notifyListeners();
  }

  bool isVideoMuted(String videoId) {
    return videoMuteStates[videoId] ?? false;
  }

  // Getters
  VideoEditorController? get videoEditorController => _videoEditorController;

  bool get isInitializingVideo => _isInitializingVideo;

  ScrollController? get videoScrollController => _videoScrollController;

  ScrollController? get audioScrollController => _audioScrollController;

  ScrollController? get textScrollController => _textScrollController;

  TextEditingController get textEditingController => _textEditingController;

  AudioPlayer? get audioController => _audioController;

  String? get selectedAudio => _selectedAudio;

  String? get currentVideoPath => _currentVideoPath;

  List<String> get assets => _assets;

  List<TextOverlay> get textOverlays => _textOverlays;

  String get currentFilter => _currentFilter;

  EditMode get editMode => _editMode;

  double get trimStart => _trimStart;

  double get trimEnd => _trimEnd;

  double get audioTrimStart => _audioTrimStart;

  double get audioTrimEnd => _audioTrimEnd;

  Rect? get cropRect => _cropRect;

  int get rotation => _rotation;

  TransitionType get selectedTransition => _selectedTransition;

  double get playbackSpeed => _playbackSpeed;

  double get videoVolume => _videoVolume;

  double get audioVolume => _audioVolume;

  bool get isPlaying => _isPlaying;

  bool get isExtractingFrames => _isExtractingFrames;

  List<String> get framePaths => _framePaths;

  List<double> get waveformData => _waveformData;

  bool get textFieldVisibility => _textFieldVisibility;

  bool get sendButtonVisibility => _sendButtonVisibility;

  String get layeredTextOnVideo => _layeredTextOnVideo;

  double get videoPosition => _videoPosition;

  double get videoDuration => _videoDuration;

  // Canvas-related getters
  bool get useCanvasForTextOverlays => _useCanvasForTextOverlays;
  Size? get previewContainerSize => _previewContainerSize;

  // Add method to get current video time for canvas
  double get currentVideoTime {
    if (_videoEditorController?.video.value.isInitialized == true) {
      return _videoEditorController!.video.value.position.inMilliseconds /
          1000.0;
    }
    return 0.0;
  }

  Duration? get selectedAudioDuration => _audioDuration;

  List<VideoCaption> get captions => _captions;
  double _playbackPosition = 0.0;

  double get playbackPosition => _playbackPosition;
  Size? recommendedAspectRatio;

  List<VideoTrackModel> get videoTracks => _videoTracks;

  List<AudioTrackModel> get audioTracks => _audioTracks;

  List<TextTrackModel> get textTracks => _textTracks;

  int get selectedVideoTrackIndex => _selectedVideoTrackIndex;

  int get selectedAudioTrackIndex => _selectedAudioTrackIndex;

  int get selectedTextTrackIndex => _selectedTextTrackIndex;

  // Preview height for text overlays
  double? get previewHeight => _previewHeight;

  void setPreviewHeight(double? height) {
    _previewHeight = height;
    notifyListeners();
  }

  void setUseCanvasForTextOverlays(bool value) {
    _useCanvasForTextOverlays = value;
    notifyListeners();
  }

  void setPreviewContainerSize(Size size) {
    _previewContainerSize = size;
    notifyListeners();
  }

  // Add position tracking
  Timer? _positionTimer;

  final Map<String, bool> audioMuteStates = {};

  toggleTextFieldVisibility(bool value) {
    _textFieldVisibility = value;
    notifyListeners();
  }

  toggleSendButtonVisibility(bool value) {
    _sendButtonVisibility = value;
    notifyListeners();
  }

  updateDisplayText(String value) {
    _layeredTextOnVideo = value;
    notifyListeners();
  }

  updateLoading(bool val) {
    loading = val;
    notifyListeners();
  }

  void setRecommendedAspectRatio(Size size) {
    recommendedAspectRatio = size;
    notifyListeners();
  }

  void setVideoTrackIndex(int index) {
    _selectedVideoTrackIndex = index;
    notifyListeners();
  }

  void setAudioTrackIndex(int index) {
    _selectedAudioTrackIndex = index;
    notifyListeners();
  }

  void setTextTrackIndex(int index) {
    _selectedTextTrackIndex = index;
    notifyListeners();
  }

  File? _originalVideoPath;
  // Initialize video
  Future<void> initializeVideo(String videoPath) async {
    try {
      _isInitializingVideo = true;
      notifyListeners();
      await _videoEditorController?.dispose();
      _videoEditorController = await VideoEditorController.file(
        File(videoPath),
      );
      await _videoEditorController?.initialize();
      await _videoEditorController?.video.setVolume(1.0);
      _isInitializingVideo = false;
      _trimEnd =
          _videoEditorController?.video.value.duration.inSeconds.toDouble() ??
              0.0;

      // Update video duration after initialization
      _videoDuration =
          _videoEditorController?.videoDuration.inSeconds.toDouble() ?? 0.0;

      // _controller?.addListener(() {
      //   _playbackPosition =
      //       _controller?.value.position.inSeconds.toDouble() ?? 0.0;
      //   notifyListeners();
      // });
      _currentVideoPath = videoPath;
      _originalVideoPath = File(videoPath);

      // Add position listener
      // _videoEditorController?.addListener(_onVideoPositionChanged);
      // Extract initial frames
      extractFrames();

      notifyListeners();
    } catch (err) {
      rethrow;
    }
  }

  Future<void> _initializeVideo(String path) async {
    await initializeVideo(path);
  }

  void togglePlay() {
    if (videoEditorController == null) return;

    try {
      // Check if controller is still valid and initialized
      if (!_videoEditorController!.video.value.isInitialized) return;

      if (isPlaying) {
        _videoEditorController?.video.pause();
      } else {
        _videoEditorController?.video.play();
      }
      _isPlaying = !_isPlaying;
      notifyListeners();
    } catch (e) {
      // Silently handle disposed controller errors during export
      debugPrint('Toggle play error (likely disposed controller): $e');
    }
  }

  void _onVideoPositionChanged() {
    if (_videoEditorController == null) return;

    try {
      // Check if controller is still valid and initialized
      if (!_videoEditorController!.video.value.isInitialized) return;

      _playbackPosition =
          _videoEditorController!.video.value.position.inSeconds.toDouble();

      // Check if position is outside trim bounds
      if (_playbackPosition < _trimStart) {
        _videoEditorController?.video.seekTo(
          Duration(seconds: _trimStart.round()),
        );
      } else if (_playbackPosition > _trimEnd) {
        _videoEditorController?.video.seekTo(
          Duration(seconds: _trimStart.round()),
        );
        _videoEditorController?.video.pause();
      }

      notifyListeners();
    } catch (e) {
      // Silently handle disposed controller errors during export
      debugPrint(
          'Video position listener error (likely disposed controller): $e');
    }
  }

  void seekTo(double position) {
    if (_videoEditorController == null) return;

    // Clamp position within trim bounds
    position = position.clamp(_trimStart, _trimEnd);
    _videoEditorController?.video.seekTo(Duration(seconds: position.round()));
    notifyListeners();
  }

  // Asset management
  void addAsset(String asset) {
    _addToUndoStack(
      EditOperation(EditOperationType.asset, List<String>.from(_assets), [
        ..._assets,
        asset,
      ]),
    );
    _assets.add(asset);
    notifyListeners();
  }

  // Audio management
  Future<void> setAudio(String audio) async {
    _selectedAudio = audio;
    _audioController?.dispose();
    _audioController = AudioPlayer();
    final file = File(audio);
    await _audioController?.setSource(DeviceFileSource(file.path));

    // Get duration in a safe way
    final duration = await _audioController?.getDuration() ?? const Duration();
    _audioDuration = duration;
    _audioTrimEnd = duration.inSeconds.toDouble();

    // Generate waveform data
    await _generateWaveformData(audio);

    notifyListeners();
  }

  Future<void> _generateWaveformData(String audioPath) async {
    _isExtractingFrames = true;
    notifyListeners();

    try {
      final command =
          '-i $audioPath -f s16le -acodec pcm_s16le -ac 1 -ar 1000 pipe:1';
      final session = await FFmpegKit.execute(command);
      final output = await session.getOutput() ?? '';

      _waveformData = output
          .split('\n')
          .where((s) => s.isNotEmpty)
          .map((s) => double.parse(s))
          .toList();
    } finally {
      _isExtractingFrames = false;
      notifyListeners();
    }
  }

  // Edit mode
  void setEditMode(EditMode mode) {
    _editMode = mode;
    notifyListeners();
  }

  // Text overlays
  void addTextOverlay(TextOverlay overlay) {
    _addToUndoStack(
      EditOperation(
        EditOperationType.text,
        List<TextOverlay>.from(_textOverlays),
        [..._textOverlays, overlay],
      ),
    );
    _textOverlays.add(overlay);
    notifyListeners();
  }

  // Filters
  void applyFilter(String filter) {
    _addToUndoStack(
      EditOperation(EditOperationType.filter, _currentFilter, filter),
    );
    _currentFilter = filter;
    notifyListeners();
  }

  // crop
  Future<void> applyCrop() async {
    print('applyCrop called');
    print('_cropRect: $_cropRect');
    if (_cropRect == null || _videoEditorController == null) return;

    // Validate aspect ratio compliance
    if (recommendedAspectRatio != null) {
      final targetRatio =
          recommendedAspectRatio!.width / recommendedAspectRatio!.height;
      final currentRatio = _cropRect!.width / _cropRect!.height;

      if ((currentRatio - targetRatio).abs() > 0.01) {
        // Auto-adjust crop to match aspect ratio
        _cropRect = _constrainToAspectRatio(_cropRect!, targetRatio);
      }
    }
    final tempDir = await getTemporaryDirectory();
    final outputPath =
        '${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}_cropped_video.mp4';

    print(
      'Cropping with: width=${_cropRect!.width}, height=${_cropRect!.height}, left=${_cropRect!.left}, top=${_cropRect!.top}',
    );

    final command = '-i ${_videoEditorController!.video.dataSource} '
        '-filter:v "crop=${_cropRect!.width.toInt()}:'
        '${_cropRect!.height.toInt()}:'
        '${_cropRect!.left.toInt()}:'
        '${_cropRect!.top.toInt()}" '
        '-c:a copy $outputPath';

    final session = await FFmpegKit.execute(command);
    final logs = await session.getAllLogsAsString();
    print('applyCrop executed');
    print('FFmpeg logs: $logs');
    if (File(outputPath).existsSync()) {
      _currentVideoPath = outputPath;
      await initializeVideo(outputPath);
      _cropRect = null;
      notifyListeners();
    }
  }

  Rect _constrainToAspectRatio(Rect rect, double targetRatio) {
    final currentRatio = rect.width / rect.height;

    if (currentRatio > targetRatio) {
      return Rect.fromLTWH(
        rect.left,
        rect.top,
        rect.height * targetRatio,
        rect.height,
      );
    } else {
      return Rect.fromLTWH(
        rect.left,
        rect.top,
        rect.width,
        rect.width / targetRatio,
      );
    }
  }

  // void updateCropRect(Rect rect) {
  //   _cropRect = rect;
  //   notifyListeners();
  // }
  void refreshPreview() {
    if (_currentVideoPath != null) {
      initializeVideo(_currentVideoPath!);
    }
  }

  // caption
  void addCaption(VideoCaption caption) {
    _addToUndoStack(
      EditOperation(
        EditOperationType.caption,
        List<VideoCaption>.from(_captions),
        [..._captions, caption],
      ),
    );
    _captions.add(caption);
    notifyListeners();
  }

  // Trim controls
  void updateTrimValues(double start, double end) {
    _addToUndoStack(
      EditOperation(
        EditOperationType.trim,
        {'start': _trimStart, 'end': _trimEnd},
        {'start': start, 'end': end},
      ),
    );
    _trimStart = start;
    _trimEnd = end;
    notifyListeners();
  }

  void updateAudioTrim(double start, double end) {
    _audioTrimStart = start;
    _audioTrimEnd = end;
    notifyListeners();
  }

  // Crop controls
  void updateCropRect(Rect rect) {
    print(
      'Provider updateCropRect: left=${rect.left}, top=${rect.top}, width=${rect.width}, height=${rect.height}',
    );
    print('Provider recommendedAspectRatio: $recommendedAspectRatio');

    // DISABLED: Don't constrain manual crops to recommended aspect ratio
    // This allows free cropping for text overlay positioning
    // if (recommendedAspectRatio != null) {
    //   final targetRatio =
    //       recommendedAspectRatio!.width / recommendedAspectRatio!.height;
    //   print('Provider constraining crop to aspect ratio: $targetRatio');
    //   final originalRect = rect;
    //   rect = _constrainToAspectRatio(rect, targetRatio);
    //   print(
    //       'Provider crop constrained: ${originalRect.width}x${originalRect.height} -> ${rect.width}x${rect.height}');
    // }

    print(
        'Provider crop NOT constrained - using original: ${rect.width}x${rect.height}');
    _addToUndoStack(EditOperation(EditOperationType.crop, _cropRect, rect));
    _cropRect = rect;
    notifyListeners();
  }

  // Future<void> applyCrop() async {
  //   if (_cropRect == null || _controller == null) return;
  //
  //   final aspectRatio = _cropRect!.width / _cropRect!.height;
  //   final command =
  //       '-i ${_controller!.dataSource} -vf "crop=${_cropRect!.width.toInt()}:${_cropRect!.height.toInt()}:${_cropRect!.left.toInt()}:${_cropRect!.top.toInt()}" -c:a copy ${_controller!.dataSource}_cropped.mp4';
  //
  //   await FFmpegKit.execute(command);
  //   await _initializeVideo('${_controller!.dataSource}_cropped.mp4');
  // }

  // Rotation controls
  void setRotation(int newRotation) {
    _addToUndoStack(
      EditOperation(EditOperationType.rotation, _rotation, newRotation),
    );
    _rotation = newRotation;
    notifyListeners();
  }

  // Method to get aspect ratio dimensions
  Size _getAspectRatioDimensions() {
    if (recommendedAspectRatio != null) {
      return recommendedAspectRatio!;
    }

    // Fallback to video dimensions if no recommended ratio is set
    if (_videoEditorController?.video.value.size != null) {
      return _videoEditorController!.video.value.size;
    }

    // Default aspect ratio (16:9 at 1080p)
    return const Size(1920, 1080);
  }

  // Updated combineVideos method with proper aspect ratio handling
  Future<void> combineVideos(List<String> assets) async {
    if (assets.isEmpty || _videoEditorController == null) return;

    final tempDir = await getTemporaryDirectory();
    final outputPath =
        '${tempDir.path}/combined_${DateTime.now().millisecondsSinceEpoch}.mp4';
    final currentVideo = _currentVideoPath ?? _originalVideoPath!.path;

    // Get target dimensions
    final targetSize = _getAspectRatioDimensions();
    final targetWidth = targetSize.width.toInt();
    final targetHeight = targetSize.height.toInt();

    // Create temp files for image assets
    List<String> processedAssets = [];
    for (String asset in assets) {
      if ([
        "jpg",
        "jpeg",
        "png",
        "webp",
      ].contains(asset.split(".").last.toLowerCase())) {
        // Convert image to video with proper aspect ratio
        final imageVideoPath =
            '${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}_img_${processedAssets.length}.mp4';

        await FFmpegKit.execute(
          '-loop 1 -t 10 -i "$asset" '
          '-vf "scale=$targetWidth:$targetHeight:force_original_aspect_ratio=decrease,'
          'pad=$targetWidth:$targetHeight:(ow-iw)/2:(oh-ih)/2:color=black" '
          '-c:v h264 -preset medium -crf 23 -pix_fmt yuv420p '
          '-r 30 "$imageVideoPath"',
        );
        processedAssets.add(imageVideoPath);
      } else {
        // Scale video to match target aspect ratio
        final scaledVideoPath =
            '${tempDir.path}/scaled_${processedAssets.length}.mp4';

        await FFmpegKit.execute(
          '-i "$asset" '
          '-vf "scale=$targetWidth:$targetHeight:force_original_aspect_ratio=decrease,'
          'pad=$targetWidth:$targetHeight:(ow-iw)/2:(oh-ih)/2:color=black" '
          '-c:v h264 -preset medium -crf 23 -r 30 '
          '"$scaledVideoPath"',
        );
        processedAssets.add(scaledVideoPath);
      }
    }

    // Scale the current video to match target aspect ratio
    final scaledCurrentVideoPath = '${tempDir.path}/scaled_current.mp4';
    await FFmpegKit.execute(
      '-i "$currentVideo" '
      '-vf "scale=$targetWidth:$targetHeight:force_original_aspect_ratio=decrease,'
      'pad=$targetWidth:$targetHeight:(ow-iw)/2:(oh-ih)/2:color=black" '
      '-c:v h264 -preset medium -crf 23 -r 30 '
      '"$scaledCurrentVideoPath"',
    );

    // Create concat file including scaled current video
    final listPath =
        '${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}_files.txt';
    final allFiles = [scaledCurrentVideoPath, ...processedAssets];

    await File(listPath).writeAsString(
      allFiles
          .map((path) => "file '${path.replaceAll("'", "'\\''")}'")
          .join('\n'),
    );

    FFmpegSession session = await FFmpegKit.execute(
      '-f concat -safe 0 -i "$listPath" -c copy "$outputPath"',
    );

    final returnCode = await session.getReturnCode();

    if (ReturnCode.isSuccess(returnCode)) {
      await initializeVideo(outputPath);
      safePrint("NEW_VIDEO_${outputPath == currentVideo}: $outputPath");
    } else {
      listAllLogs(session);
    }
  }

  void updateAssets(List<String> newAssets) {
    _assets = newAssets;
    notifyListeners();
  }

  Future<void> applyRotation() async {
    if (_videoEditorController?.video == null || _rotation == 0) return;

    final tempDir = await getTemporaryDirectory();
    final outputPath = '${tempDir.path}/rotated_video.mp4';

    final command =
        '-i ${_videoEditorController!.video.dataSource} -vf "rotate=${_rotation * pi / 180}" -c:a copy $outputPath';
    await FFmpegKit.execute(command);

    await _initializeVideo(outputPath);
    // ✅ FIXED: Don't reset rotation to 0 - preserve it for preview system
    // _rotation = 0;  // ❌ REMOVED: This was preventing rotation detection
    notifyListeners();
  }

  // Frame extraction
  Future<void> extractFrames() async {
    if (_videoEditorController == null || _isExtractingFrames) return;

    _isExtractingFrames = true;
    notifyListeners();

    try {
      _framePaths = await FrameExtractor.extractFrames(
        videoPath: _videoEditorController!.video.dataSource,
        frameCount: 10,
        videoDuration: _videoEditorController!.video.value.duration,
      );
    } finally {
      _isExtractingFrames = false;
      notifyListeners();
    }
  }

  // Playback controls
  void setPlaybackSpeed(double speed) {
    _addToUndoStack(
      EditOperation(EditOperationType.speed, _playbackSpeed, speed),
    );
    _playbackSpeed = speed;
    _videoEditorController?.video.setPlaybackSpeed(speed);
    notifyListeners();
  }

  void setVideoVolume(double volume) {
    _videoVolume = volume;
    _videoEditorController?.video.setVolume(volume);
    notifyListeners();
  }

  void setAudioVolume(double volume) {
    _audioVolume = volume;
    _audioController?.setVolume(volume);
    notifyListeners();
  }

  // Transitions
  void setTransition(TransitionType transition) {
    _addToUndoStack(
      EditOperation(
        EditOperationType.transition,
        _selectedTransition,
        transition,
      ),
    );
    _selectedTransition = transition;
    notifyListeners();
  }

  // Undo/Redo functionality
  void _addToUndoStack(EditOperation operation) {
    _undoStack.push(operation);
    _redoStack.clear();
  }

  void undo() {
    if (_undoStack.isEmpty) return;

    final operation = _undoStack.pop();
    _redoStack.push(operation);
    _applyOperation(operation.reverse());
    notifyListeners();
  }

  void redo() {
    if (_redoStack.isEmpty) return;

    final operation = _redoStack.pop();
    _undoStack.push(operation);
    _applyOperation(operation);
    notifyListeners();
  }

  void _applyOperation(EditOperation operation) {
    switch (operation.type) {
      case EditOperationType.text:
        _textOverlays = operation.newState as List<TextOverlay>;
        break;
      case EditOperationType.filter:
        _currentFilter = operation.newState as String;
        break;
      case EditOperationType.trim:
        final Map<String, double> values =
            operation.newState as Map<String, double>;
        _trimStart = values['start']!;
        _trimEnd = values['end']!;
        break;
      case EditOperationType.crop:
        _cropRect = operation.newState as Rect?;
        break;
      case EditOperationType.rotation:
        _rotation = operation.newState as int;
        break;
      case EditOperationType.transition:
        _selectedTransition = operation.newState as TransitionType;
        break;
      case EditOperationType.speed:
        _playbackSpeed = operation.newState as double;
        _videoEditorController?.video.setPlaybackSpeed(_playbackSpeed);
        break;
      case EditOperationType.asset:
        _assets = operation.newState as List<String>;
        break;
      case EditOperationType.caption:
        _captions = operation.newState as List<VideoCaption>;
        break;
      case EditOperationType.stretch:
        // Stretch operations are complex and require special handling
        // For now, we'll just trigger a full refresh
        notifyListeners();
        break;
    }
  }

  // Updated exportVideo method to ensure aspect ratio is maintained
  Future<String> exportVideo(
    BuildContext context, {
    required String inputPath,
    required String outputPath,
  }) async {
    try {
      print('=== Starting exportVideo with mute processing ===');

      // Get target dimensions
      final targetSize = _getAspectRatioDimensions();

      // Use our combineSegments method that handles mute states
      final combinedVideoPath = await combineSegments(_videoTracks);
      if (combinedVideoPath == null) {
        throw Exception('Failed to combine video segments for export');
      }

      print('Video segments combined with mute processing: $combinedVideoPath');

      // Use the combined video as the input for further export steps
      final exportVideoPath = await VideoExportManager.exportVideo(
        context,
        inputPath: combinedVideoPath,
        outputPath: outputPath,
        startTime: trimStart,
        endTime: trimEnd,
        audioPath: selectedAudio,
        audioTrimStart: audioTrimStart,
        audioTrimEnd: audioTrimEnd,
        textOverlays: textOverlays,
        filter: currentFilter,
        transition: selectedTransition,
        speed: playbackSpeed,
        videoVolume: videoVolume,
        audioVolume: audioVolume,
        rotation: videoEditorController?.rotation,
        minCrop: videoEditorController?.minCrop,
        maxCrop: videoEditorController?.maxCrop,
        videoHeight: targetSize.height.toDouble(), // Use target height
        videoWidth: targetSize.width.toDouble(), // Use target width
        previewHeight:
            _previewHeight ?? 370.0, // Use actual preview height or fallback
        videoTracks: _videoTracks,
        textTracks: _textTracks,
        editorProvider: this,
      );

      print('Export completed successfully: $exportVideoPath');
      return exportVideoPath;
    } catch (e) {
      print('Export failed with error: $e');
      print(e);
      rethrow;
    }
  }

  // Helper method to validate aspect ratio
  Future<bool> _validateAspectRatio(String videoPath) async {
    final session = await FFmpegKit.execute(
      '-i "$videoPath" -hide_banner 2>&1',
    );
    final output = await session.getOutput() ?? '';

    // Extract video dimensions from FFmpeg output
    final dimensionRegex = RegExp(r'(\d+)x(\d+)');
    final match = dimensionRegex.firstMatch(output);

    if (match != null) {
      final width = int.parse(match.group(1)!);
      final height = int.parse(match.group(2)!);
      final targetSize = _getAspectRatioDimensions();

      return width == targetSize.width.toInt() &&
          height == targetSize.height.toInt();
    }

    return false;
  }

  // Method to force aspect ratio correction on any video
  Future<String?> _forceAspectRatioCorrection(String inputPath) async {
    final targetSize = _getAspectRatioDimensions();
    final targetWidth = targetSize.width.toInt();
    final targetHeight = targetSize.height.toInt();

    final tempDir = await getTemporaryDirectory();
    final outputPath =
        '${tempDir.path}/aspect_corrected_${DateTime.now().millisecondsSinceEpoch}.mp4';

    final command = '-i "$inputPath" '
        '-vf "scale=$targetWidth:$targetHeight:force_original_aspect_ratio=decrease,'
        'pad=$targetWidth:$targetHeight:(ow-iw)/2:(oh-ih)/2:color=black" '
        '-c:v h264 -preset medium -crf 23 -r 30 -c:a copy "$outputPath"';

    final session = await FFmpegKit.execute(command);
    final returnCode = await session.getReturnCode();

    if (ReturnCode.isSuccess(returnCode)) {
      return outputPath;
    } else {
      final logs = await session.getOutput();
      print('Aspect ratio correction error: $logs');
      return null;
    }
  }

  // Helper to write debug logs to a file
  Future<void> _writeLog(String message) async {
    try {
      final dir = await getApplicationDocumentsDirectory();
      final logFile = File('${dir.path}/video_export_debug.log');
      final timestamp = DateTime.now().toString();
      await logFile.writeAsString('[$timestamp] $message\n',
          mode: FileMode.append);
    } catch (e) {
      print('Failed to write log: $e');
    }
  }

  // Helper to probe video size (width, height)
  Future<Size?> probeVideoSize(String videoPath) async {
    final session = await FFmpegKit.execute('-i "$videoPath" -hide_banner');
    final output = await session.getOutput() ?? '';
    final logs = await session.getLogsAsString() ?? '';
    final allOutput = output + logs;
    // Look for a line like: Stream #0:0: Video: h264 ... 1440x1440 ...
    final regex = RegExp(r'Video: [^,]+, [^,]+, (\d+)x(\d+)');
    final match = regex.firstMatch(allOutput);
    if (match != null) {
      final width = int.tryParse(match.group(1) ?? '0') ?? 0;
      final height = int.tryParse(match.group(2) ?? '0') ?? 0;
      if (width > 0 && height > 0) {
        return Size(width.toDouble(), height.toDouble());
      }
    }
    return null;
  }

  // Helper: Ensure a video file has audio (add silent audio if missing)
  Future<File> ensureAudio(File videoFile) async {
    // Check if video has audio
    final session =
        await FFmpegKit.execute('-i "${videoFile.path}" -hide_banner');
    final output = await session.getOutput() ?? '';
    final logs = await session.getLogsAsString() ?? '';
    final allOutput = output + logs;
    if (allOutput.contains('Audio:')) {
      return videoFile;
    }
    // Add silent audio
    final tempDir = await getTemporaryDirectory();
    final outputPath =
        '${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}_withaudio.mp4';
    final command =
        '-y -i "${videoFile.path}" -f lavfi -i anullsrc=channel_layout=stereo:sample_rate=48000 -shortest -c:v copy -c:a aac "$outputPath"';
    print('Adding silent audio: $command');
    final addSession = await FFmpegKit.execute(command);
    final returnCode = await addSession.getReturnCode();
    if (ReturnCode.isSuccess(returnCode)) {
      print('Silent audio added: $outputPath');
      return File(outputPath);
    } else {
      print('Failed to add silent audio, using original: ${videoFile.path}');
      return videoFile;
    }
  }

  /// Combine all video segments into a single video file (no transitions)
  Future<String?> combineSegments(List<VideoTrackModel> videoTracks) async {
    if (videoTracks.isEmpty) return null;
    final tempDir = await getTemporaryDirectory();
    final outputPath =
        '${tempDir.path}/combined_${DateTime.now().millisecondsSinceEpoch}.mp4';

    print('=== Starting combineSegments with mute processing ===');
    print('Number of video tracks: ${videoTracks.length}');

    // First, process segments for mute state
    List<File> processedSegments = [];
    for (int i = 0; i < videoTracks.length; i++) {
      final track = videoTracks[i];
      final isMuted = isVideoMuted(track.id);

      print(
          'Processing segment $i: id=${track.id}, muted=$isMuted, path=${track.processedFile.path}');

      if (isMuted) {
        // Step 1: Set original audio volume to 0
        final tempMutedPath =
            '${tempDir.path}/temp_muted_segment_${i}_${DateTime.now().millisecondsSinceEpoch}.mp4';
        final volumeMuteCmd =
            '-y -i "${track.processedFile.path}" -af "volume=0" -c:v copy -c:a aac "$tempMutedPath"';
        print('Muting original audio for segment $i: $volumeMuteCmd');
        final muteSession = await FFmpegKit.execute(volumeMuteCmd);
        final muteReturnCode = await muteSession.getReturnCode();
        if (!ReturnCode.isSuccess(muteReturnCode)) {
          print('Failed to mute original audio for segment $i, using original');
          processedSegments.add(track.processedFile);
          continue;
        }

        // Step 2: Add silent audio using anullsrc
        final mutedPath =
            '${tempDir.path}/muted_segment_${i}_${DateTime.now().millisecondsSinceEpoch}.mp4';
        final anullsrcCmd =
            '-y -i "$tempMutedPath" -f lavfi -i anullsrc=channel_layout=stereo:sample_rate=48000 -shortest -c:v copy -c:a aac "$mutedPath"';
        print('Adding silent audio for segment $i: $anullsrcCmd');
        final anullsrcSession = await FFmpegKit.execute(anullsrcCmd);
        final anullsrcReturnCode = await anullsrcSession.getReturnCode();
        if (ReturnCode.isSuccess(anullsrcReturnCode)) {
          processedSegments.add(File(mutedPath));
          print('Muted segment $i processed successfully: $mutedPath');
          // Verify the muted segment has silent audio
          final verifySession =
              await FFmpegKit.execute('-i "$mutedPath" -hide_banner');
          final verifyOutput = await verifySession.getOutput() ?? '';
          final verifyLogs = await verifySession.getLogsAsString() ?? '';
          final allVerifyOutput = verifyOutput + verifyLogs;
          print(
              'Muted segment $i audio check: ${allVerifyOutput.contains('Audio:') ? 'Has audio stream' : 'No audio stream'}');
          if (allVerifyOutput.contains('Audio:')) {
            print(
                'Muted segment $i audio details: ${allVerifyOutput.split('Audio:').last.split('\n').first}');
          }
        } else {
          print('Failed to add silent audio for segment $i, using original');
          processedSegments.add(track.processedFile);
        }
      } else {
        // Not muted, use original file
        final fileWithAudio = await ensureAudio(track.processedFile);
        processedSegments.add(fileWithAudio);
        print('Segment $i processed (not muted): ${fileWithAudio.path}');
        // Verify the segment has audio
        final verifySession =
            await FFmpegKit.execute('-i "${fileWithAudio.path}" -hide_banner');
        final verifyOutput = await verifySession.getOutput() ?? '';
        final verifyLogs = await verifySession.getLogsAsString() ?? '';
        final allVerifyOutput = verifyOutput + verifyLogs;
        print(
            'Segment $i audio stream check: ${allVerifyOutput.contains('Audio:') ? 'Has audio stream' : 'No audio stream'}');
        if (allVerifyOutput.contains('Audio:')) {
          print(
              'Segment $i audio details: ${allVerifyOutput.split('Audio:').last.split('\n').first}');
        }
      }
    }

    // Ensure all segments have audio
    List<File> audioSafeSegments = [];
    for (int i = 0; i < processedSegments.length; i++) {
      final fileWithAudio = await ensureAudio(processedSegments[i]);
      audioSafeSegments.add(fileWithAudio);
      print('Segment $i audio-safe: ${fileWithAudio.path}');
      // Print audio stream info for verification
      final verifySession =
          await FFmpegKit.execute('-i "${fileWithAudio.path}" -hide_banner');
      final verifyOutput = await verifySession.getOutput() ?? '';
      final verifyLogs = await verifySession.getLogsAsString() ?? '';
      final allVerifyOutput = verifyOutput + verifyLogs;
      print(
          'Segment $i audio stream check: ${allVerifyOutput.contains('Audio:') ? 'Has audio stream' : 'No audio stream'}');
      if (allVerifyOutput.contains('Audio:')) {
        print(
            'Segment $i audio details: ${allVerifyOutput.split('Audio:').last.split('\n').first}');
      }
    }

    // Create a file list for FFmpeg concat
    final listPath =
        '${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}_input_list.txt';
    final fileList = audioSafeSegments
        .map((file) => "file '${file.path.replaceAll("'", "'\\''")}'")
        .join('\n');
    await File(listPath).writeAsString(fileList);

    // Use -map 0:v -map 0:a? to ensure audio is included if present
    final command =
        '-y -f concat -safe 0 -i "$listPath" -map 0:v -map 0:a? -c:v libx264 -preset ultrafast -c:a aac -b:a 800k "$outputPath"';
    print('Combining segments command: $command');
    final session = await FFmpegKit.execute(command);
    final returnCode = await session.getReturnCode();
    if (ReturnCode.isSuccess(returnCode)) {
      print('Segments combined successfully: $outputPath');
      // Check if output has audio
      bool hasAudio = await _hasAudioStream(outputPath);
      print('Combined output has audio: $hasAudio');
      if (!hasAudio) {
        // Add silent audio as fallback
        print('No audio detected in combined output, adding silent audio.');
        final fileWithAudio = await ensureAudio(File(outputPath));
        print('Silent audio added to combined output: ${fileWithAudio.path}');
        return fileWithAudio.path;
      }
      return outputPath;
    } else {
      final logs = await session.getOutput();
      print('Failed to combine segments: $logs');
      return null;
    }
  }

  // Helper to check if a video file has an audio stream
  Future<bool> _hasAudioStream(String filePath) async {
    final session = await FFmpegKit.execute('-i "$filePath" -hide_banner');
    final output = await session.getOutput() ?? '';
    final logs = await session.getLogsAsString() ?? '';
    final allOutput = output + logs;
    return allOutput.contains('Audio:');
  }

  /// Only merge audio tracks with the already combined video
  Future<String?> mergeMultipleAudioToVideo(
    BuildContext context, {
    required String combinedVideoPath,
  }) async {
    final tempDir = await getTemporaryDirectory();

    await _writeLog('=== Starting mergeMultipleAudioToVideo ===');
    await _writeLog('Number of audio tracks: ${_audioTracks.length}');
    print('=== Starting mergeMultipleAudioToVideo ===');
    print('Number of audio tracks: ${_audioTracks.length}');

    // If no additional audio tracks, return the combined video
    if (_audioTracks.isEmpty) {
      await _writeLog('No additional audio tracks, returning combined video');
      print('No additional audio tracks, returning combined video');
      return combinedVideoPath;
    }

    // Merge additional audio tracks with the combined video (audio is guaranteed to be present)
    await _writeLog('Merging additional audio tracks with combined video...');
    print('Merging additional audio tracks with combined video...');
    return await _mergeAudioTracksWithVideo(combinedVideoPath);
  }

  Future<int> getMediaDuration(String filePath) async {
    final session = await FFmpegKit.execute('-i "$filePath" 2>&1');
    final logs = await session.getOutput();

    RegExp durationRegex = RegExp(r"Duration:\s(\d+):(\d+):(\d+)");
    final match = durationRegex.firstMatch(logs ?? '');

    if (match != null) {
      int hours = int.parse(match.group(1)!);
      int minutes = int.parse(match.group(2)!);
      int seconds = int.parse(match.group(3)!);
      return (hours * 3600) + (minutes * 60) + seconds;
    }

    return 0;
  }

  Future<File?> trimGeneratedAudio({
    required File audioFile,
    required double audioDuration,
    required double startDuration,
    required double endDuration,
  }) async {
    final String inputPath = audioFile.path;
    final Directory tempDir = await getTemporaryDirectory();
    final String outputPath =
        "${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}_processed_audio.mp3";

    String command;
    double trimDuration = endDuration - startDuration;

    if (!File(inputPath).existsSync()) {
      print("Error: Input file does not exist: $inputPath");
      return null;
    }

    if (audioDuration > trimDuration) {
      command =
          "-i '$inputPath' -ss $startDuration -t $trimDuration -c copy $outputPath";
    } else {
      return audioFile;
    }

    final session = await FFmpegKit.execute(command);
    final returnCode = await session.getReturnCode();

    if (ReturnCode.isSuccess(returnCode)) {
      final int outputDuration = await getMediaDuration(outputPath);
      if (outputDuration > trimDuration) {
        final String trimmedOutputPath =
            "${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}_trimmed_audio.mp3";
        final sessionTrim = await FFmpegKit.execute(
          "-i '$outputPath' -t $trimDuration -c copy $trimmedOutputPath",
        );
        return ReturnCode.isSuccess(await sessionTrim.getReturnCode())
            ? File(trimmedOutputPath)
            : null;
      }
      return File(outputPath);
    }
    return null;
  }

  Future<File?> trimAudio({
    required File audioFile,
    required double audioDuration,
    required double startDuration,
    required double endDuration,
  }) async {
    final String inputPath = audioFile.path;
    final Directory tempDir = await getTemporaryDirectory();
    final String outputPath =
        "${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}_processed_audio.mp3";

    if (!File(inputPath).existsSync()) {
      print("Error: Input file does not exist: $inputPath");
      return null;
    }

    double trimDuration = endDuration - startDuration;
    if (trimDuration <= 0) {
      print("Error: Invalid trim duration: $trimDuration");
      return audioFile;
    }

    String command;

    if (audioDuration > trimDuration) {
      // Trim audio
      command =
          "-i '$inputPath' -ss $startDuration -t $trimDuration -c:a aac -b:a 128k '$outputPath'";
    } else if (audioDuration < trimDuration) {
      // Loop audio to match duration
      int loops = (trimDuration / audioDuration).ceil();
      command =
          "-stream_loop $loops -i '$inputPath' -t $trimDuration -c:a aac -b:a 128k '$outputPath'";
    } else {
      // Duration matches, just re-encode
      command = "-i '$inputPath' -c:a aac -b:a 128k '$outputPath'";
    }

    print('Trim audio command: $command');
    final session = await FFmpegKit.execute(command);
    final returnCode = await session.getReturnCode();

    if (ReturnCode.isSuccess(returnCode)) {
      return File(outputPath);
    } else {
      final logs = await session.getOutput();
      print('Audio trim error: $logs');
      return null;
    }
  }

  // Additional helper method to validate audio files
  Future<bool> validateAudioFile(File audioFile) async {
    if (!audioFile.existsSync()) {
      print('Audio file does not exist: ${audioFile.path}');
      return false;
    }

    final session = await FFmpegKit.execute(
      '-i "${audioFile.path}" -hide_banner -t 1 -f null - 2>&1',
    );
    final output = await session.getOutput() ?? '';

    if (output.contains('Invalid data found') ||
        output.contains('No such file')) {
      print('Invalid audio file: ${audioFile.path}');
      return false;
    }

    return true;
  }

  Future<void> combineMediaFiles(
    List<File> reorderFiles,
    List<int> totalDuration,
  ) async {
    // updateLoading(true);
    final (
      File? resultVideo,
      List<(File, File)>? processedFiles,
    ) = await EditorVideoController.combineMediaFiles(
      reorderFiles,
      totalDuration: totalDuration,
      outputHeight: recommendedAspectRatio?.height.toInt() ?? 0,
      outputWidth: recommendedAspectRatio?.width.toInt() ?? 0,
    );
    if (resultVideo != null && processedFiles != null) {
      await reset(
        resultVideo.path,
        originalFile: reorderFiles,
        processedFile: processedFiles.map((e) => e.$2).toList() ?? [],
      );
    }
  }

  // reset
  Future<void> reset(
    String videoPath, {
    Size? recommendedSize,
    required List<File> processedFile,
    required List<File> originalFile,
    List<AudioTrackModel>? preserveAudioTracks,
    List<TextTrackModel>? preserveTextTracks,
    String? preserveOverlayText,
    Map<String, bool>? preserveMuteStates,
  }) async {
    // DEBUG LOGGING
    print('DEBUG: reset called with:');
    print('  originalFile:');
    for (var f in originalFile) print('    ' + f.path);
    print('  processedFile:');
    for (var f in processedFile) print('    ' + f.path);
    if (preserveMuteStates != null) {
      print('  preserveMuteStates: $preserveMuteStates');
    }

    // Save current mute states before clearing anything
    final currentMuteStates = Map<String, bool>.from(videoMuteStates);

    // Clean up old resources
    _audioController?.dispose();

    // Reset all values to default
    _videoVolume = 1.0;
    _audioVolume = 1.0;
    // ✅ FIXED: Don't reset rotation to 0 - preserve it for preview system
    // _rotation = 0;  // ❌ REMOVED: This was preventing rotation detection
    _trimStart = 0.0;
    _trimEnd = 0.0;
    _selectedTransition = TransitionType.none;
    _currentFilter = 'none';
    _cropRect = null;

    // Only clear if not preserving
    if (preserveOverlayText != null) {
      _layeredTextOnVideo = preserveOverlayText;
    } else {
      _layeredTextOnVideo = '';
    }

    if (preserveAudioTracks != null) {
      _audioTracks = List.from(preserveAudioTracks);
    } else {
      _audioTracks.clear();
    }

    if (preserveTextTracks != null) {
      _textTracks = List.from(preserveTextTracks);
    } else {
      _textTracks.clear();
    }

    // Use preserved mute states if provided, otherwise use current states
    final muteStatesToPreserve = preserveMuteStates ?? currentMuteStates;
    print('Using mute states for preservation: $muteStatesToPreserve');

    await _generateVideoTracks(
      originalFile: originalFile,
      processedFile: processedFile,
      preserveMuteStates: muteStatesToPreserve,
    );

    // Initialize with new video
    await initializeVideo(videoPath);
    await initializeOrResetControllers();
    _videoDuration =
        _videoEditorController?.videoDuration.inSeconds.toDouble() ?? 0.0;
    videoEditorController?.video.removeListener(listenVideoPosition);
    videoEditorController?.video.addListener(listenVideoPosition);
    // updateLoading(false);
    if (recommendedSize != null) {
      setRecommendedAspectRatio(recommendedSize);
    }

    // Verify mute states were preserved
    print('Final mute states after reset: $videoMuteStates');
    print(
        'Video tracks after reset: ${_videoTracks.map((t) => '${t.id}: ${t.originalFile.path}').toList()}');
  }

  listenVideoPosition() {
    try {
      // Check if controller is still valid
      if (_videoEditorController == null) return;

      _videoPosition =
          (_videoEditorController?.videoPosition.inSeconds ?? 0).toDouble();
      // print('Preview position: [33m[1m[4m[7m${_videoPosition}s[0m');
      // print('Segments:');
      // for (var t in _videoTracks) {
      //   print('  id: [36m${t.id}[0m, start: ${t.startTime}, end: ${t.endTime}');
      // }
      // Find the current video segment based on playback position
      VideoTrackModel? currentTrack;
      if (_videoTracks.isNotEmpty) {
        try {
          currentTrack = _videoTracks.firstWhere(
            (track) =>
                _videoPosition >= track.startTime &&
                _videoPosition < track.endTime,
          );
        } catch (e) {
          currentTrack = _videoTracks.last;
        }
      }
      if (currentTrack != null) {
        // print('Current segment: id: [32m${currentTrack.id}[0m, start: ${currentTrack.startTime}, end: ${currentTrack.endTime}, muted: ${isVideoMuted(currentTrack.id)}');
        final isMuted = isVideoMuted(currentTrack.id);
        _videoEditorController?.video.setVolume(isMuted ? 0.0 : 1.0);
      }

      notifyListeners();
    } catch (e) {
      // Silently handle disposed controller errors during export
      debugPrint(
          'Video position listener error (likely disposed controller): $e');
    }
  }

  Future<void> initializeOrResetControllers() async {
    _videoEditorController?.video.pause();

    // Get current position to avoid seeking to the same position
    final currentPosition =
        _videoEditorController?.video.value.position.inSeconds ?? 0;

    // Only seek to 0 if we're not already at position 0 to avoid blank screen issue
    if (currentPosition != 0) {
      await _videoEditorController?.video.seekTo(Duration(seconds: 0));
    } else {
      // If already at position 0, seek to a very small position and back to ensure proper rendering
      await _videoEditorController?.video.seekTo(Duration(milliseconds: 100));
      await Future.delayed(Duration(
          milliseconds: 50)); // Small delay to ensure frame is rendered
      await _videoEditorController?.video.seekTo(Duration(seconds: 0));
    }

    _linkedScrollControllerGroup.resetScroll();
    if (_videoScrollController == null)
      _videoScrollController = await _linkedScrollControllerGroup.addAndGet();
    if (_audioScrollController == null)
      _audioScrollController = await _linkedScrollControllerGroup.addAndGet();
    if (_textScrollController == null)
      _textScrollController = await _linkedScrollControllerGroup.addAndGet();
  }

  Future<void> _generateVideoTracks({
    required List<File> processedFile,
    required List<File> originalFile,
    Map<String, bool>? preserveMuteStates,
  }) async {
    // Save old track info before clearing
    final oldTrackInfos = _videoTracks
        .map((t) => (t.id, t.originalFile.path, t.totalDuration, t.startTime))
        .toList();
    final oldMuteStates =
        preserveMuteStates ?? Map<String, bool>.from(videoMuteStates);
    _videoTracks.clear();
    setVideoTrackIndex(0);
    int currentTime = 0;
    videoMuteStates.clear();

    // First pass: Create all tracks and try to match with old tracks
    for (int i = 0; i < processedFile.length; i++) {
      final totalDuration = await getMediaDuration(processedFile[i].path);
      bool hasOriginalAudio = false;
      try {
        final probeSession = await FFmpegKit.execute(
          '-hide_banner -i "${originalFile[i].path}"',
        );
        final probeLogs = await probeSession.getOutput();
        hasOriginalAudio = probeLogs?.contains('Audio:') == true;
      } catch (e) {
        hasOriginalAudio = false;
      }

      // Try to find matching old track based on file path and position
      String newId = const Uuid().v4(); // Initialize with a new ID by default
      bool foundMatch = false;

      // First try to match by index if paths match
      if (i < oldTrackInfos.length &&
          oldTrackInfos[i].$2 == originalFile[i].path) {
        newId = oldTrackInfos[i].$1;
        foundMatch = true;
      } else {
        // If no match by index, try to find a match by file path and approximate position
        for (final oldTrack in oldTrackInfos) {
          if (oldTrack.$2 == originalFile[i].path &&
              (oldTrack.$4 - currentTime).abs() < totalDuration) {
            newId = oldTrack.$1;
            foundMatch = true;
            break;
          }
        }
      }

      // Check if this is an image-based video
      final isImageBased = _isImageFile(originalFile[i].path);

      final track = VideoTrackModel(
        id: newId,
        originalFile: originalFile[i],
        processedFile: processedFile[i],
        startTime: currentTime,
        endTime: currentTime + totalDuration,
        totalDuration: totalDuration,
        hasOriginalAudio: hasOriginalAudio,
        isImageBased: isImageBased,
      );
      _videoTracks.add(track);

      // Preserve mute state if we found a match, otherwise use default (unmuted)
      videoMuteStates[track.id] =
          foundMatch ? (oldMuteStates[track.id] ?? false) : false;

      currentTime += totalDuration;
    }

    // Clean up any orphaned mute states
    videoMuteStates
        .removeWhere((id, _) => !_videoTracks.any((t) => t.id == id));

    // Debug print for duplicate IDs
    final idSet = <String>{};
    for (final t in _videoTracks) {
      if (idSet.contains(t.id)) {
        print('DUPLICATE ID FOUND: ${t.id}');
      }
      idSet.add(t.id);
    }
    print('All video track IDs: ${_videoTracks.map((t) => t.id).toList()}');
    print('All mute states: $videoMuteStates');
  }

  // Add a video track sequentially (stacking)
  Future<void> addVideoTrack(
    File originalFile,
    File processedFile,
    int totalDuration,
  ) async {
    int startTime = _videoTracks.isNotEmpty ? _videoTracks.last.endTime : 0;
    int endTime = startTime + totalDuration;

    // Check if this is an image-based video
    final isImageBased = _isImageFile(originalFile.path);

    final track = VideoTrackModel(
      originalFile: originalFile,
      processedFile: processedFile,
      startTime: startTime,
      endTime: endTime,
      totalDuration: totalDuration,
      isImageBased: isImageBased,
    );
    _videoTracks.add(track);
    _videoDuration =
        _videoTracks.fold(0, (sum, t) => sum + t.totalDuration).toDouble();

    if (_videoTracks.length == 1) {
      await initializeVideo(processedFile.path);
    }
    notifyListeners();
  }

  // Remove a video track and recalculate start/end times, preserving IDs
  Future<void> removeVideoTrack(int index) async {
    if (index < 0 || index >= _videoTracks.length) return;
    final deletedTrack = _videoTracks[index];

    // Remove the track at the given index
    _videoTracks.removeAt(index);

    // Remove mute state for this segment
    videoMuteStates.remove(deletedTrack.id);

    // --- AUDIO TRACKS ---
    List<AudioTrackModel> newAudioTracks = [];
    for (final audio in _audioTracks) {
      // Entirely before deleted segment: keep as is
      if (audio.trimEndTime <= deletedTrack.startTime) {
        print(
            'Audio track [${audio.trimStartTime}, ${audio.trimEndTime}] kept (before deleted segment)');
        newAudioTracks.add(audio);
        continue;
      }
      // Fully within deleted segment: remove
      if (audio.trimStartTime >= deletedTrack.startTime &&
          audio.trimEndTime <= deletedTrack.endTime) {
        print(
            'Audio track [${audio.trimStartTime}, ${audio.trimEndTime}] removed (fully within deleted segment)');
        continue;
      }
      // Spans before and after deleted segment: split into two
      if (audio.trimStartTime < deletedTrack.startTime &&
          audio.trimEndTime > deletedTrack.endTime) {
        print(
            'Audio track [${audio.trimStartTime}, ${audio.trimEndTime}] split (spans deleted segment)');
        newAudioTracks.add(audio.copyWith(
          id: const Uuid().v4(),
          trimStartTime: audio.trimStartTime,
          trimEndTime: deletedTrack.startTime.toDouble(),
        ));
        newAudioTracks.add(audio.copyWith(
          id: const Uuid().v4(),
          trimStartTime:
              (deletedTrack.endTime - deletedTrack.totalDuration).toDouble(),
          trimEndTime:
              (audio.trimEndTime - deletedTrack.totalDuration).toDouble(),
        ));
        continue;
      }
      // Overlaps start of deleted segment
      if (audio.trimStartTime < deletedTrack.startTime &&
          audio.trimEndTime > deletedTrack.startTime &&
          audio.trimEndTime <= deletedTrack.endTime) {
        print(
            'Audio track [${audio.trimStartTime}, ${audio.trimEndTime}] trimmed (overlaps start of deleted segment)');
        newAudioTracks.add(audio.copyWith(
          trimStartTime: audio.trimStartTime,
          trimEndTime: deletedTrack.startTime.toDouble(),
        ));
        continue;
      }
      // Overlaps end of deleted segment
      if (audio.trimStartTime >= deletedTrack.startTime &&
          audio.trimStartTime < deletedTrack.endTime &&
          audio.trimEndTime > deletedTrack.endTime) {
        print(
            'Audio track [${audio.trimStartTime}, ${audio.trimEndTime}] trimmed (overlaps end of deleted segment)');
        newAudioTracks.add(audio.copyWith(
          trimStartTime:
              (deletedTrack.endTime - deletedTrack.totalDuration).toDouble(),
          trimEndTime:
              (audio.trimEndTime - deletedTrack.totalDuration).toDouble(),
        ));
        continue;
      }
      // Starts after deleted segment: shift
      if (audio.trimStartTime >= deletedTrack.endTime) {
        print(
            'Audio track [${audio.trimStartTime}, ${audio.trimEndTime}] shifted (after deleted segment)');
        newAudioTracks.add(audio.copyWith(
          trimStartTime:
              (audio.trimStartTime - deletedTrack.totalDuration).toDouble(),
          trimEndTime:
              (audio.trimEndTime - deletedTrack.totalDuration).toDouble(),
        ));
        continue;
      }
      // Otherwise, keep as is
      print(
          'Audio track [${audio.trimStartTime}, ${audio.trimEndTime}] kept (default case)');
      newAudioTracks.add(audio);
    }
    _audioTracks = newAudioTracks;

    // --- TEXT TRACKS ---
    List<TextTrackModel> newTextTracks = [];
    for (final text in _textTracks) {
      // Entirely before deleted segment: keep as is
      if (text.trimEndTime <= deletedTrack.startTime) {
        print(
            'Text track [${text.trimStartTime}, ${text.trimEndTime}] kept (before deleted segment)');
        newTextTracks.add(text);
        continue;
      }
      // Fully within deleted segment: remove
      if (text.trimStartTime >= deletedTrack.startTime &&
          text.trimEndTime <= deletedTrack.endTime) {
        print(
            'Text track [${text.trimStartTime}, ${text.trimEndTime}] removed (fully within deleted segment)');
        continue;
      }
      // Spans before and after deleted segment: split into two
      if (text.trimStartTime < deletedTrack.startTime &&
          text.trimEndTime > deletedTrack.endTime) {
        print(
            'Text track [${text.trimStartTime}, ${text.trimEndTime}] split (spans deleted segment)');
        newTextTracks.add(text.copyWith(
          id: const Uuid().v4(),
          startTime: text.trimStartTime,
          endTime: deletedTrack.startTime.toDouble(),
        ));
        newTextTracks.add(text.copyWith(
          id: const Uuid().v4(),
          startTime:
              (deletedTrack.endTime - deletedTrack.totalDuration).toDouble(),
          endTime: (text.trimEndTime - deletedTrack.totalDuration).toDouble(),
        ));
        continue;
      }
      // Overlaps start of deleted segment
      if (text.trimStartTime < deletedTrack.startTime &&
          text.trimEndTime > deletedTrack.startTime &&
          text.trimEndTime <= deletedTrack.endTime) {
        print(
            'Text track [${text.trimStartTime}, ${text.trimEndTime}] trimmed (overlaps start of deleted segment)');
        newTextTracks.add(text.copyWith(
          startTime: text.trimStartTime,
          endTime: deletedTrack.startTime.toDouble(),
        ));
        continue;
      }
      // Overlaps end of deleted segment
      if (text.trimStartTime >= deletedTrack.startTime &&
          text.trimStartTime < deletedTrack.endTime &&
          text.trimEndTime > deletedTrack.endTime) {
        print(
            'Text track [${text.trimStartTime}, ${text.trimEndTime}] trimmed (overlaps end of deleted segment)');
        newTextTracks.add(text.copyWith(
          startTime:
              (deletedTrack.endTime - deletedTrack.totalDuration).toDouble(),
          endTime: (text.trimEndTime - deletedTrack.totalDuration).toDouble(),
        ));
        continue;
      }
      // Starts after deleted segment: shift
      if (text.trimStartTime >= deletedTrack.endTime) {
        print(
            'Text track [${text.trimStartTime}, ${text.trimEndTime}] shifted (after deleted segment)');
        newTextTracks.add(text.copyWith(
          startTime:
              (text.trimStartTime - deletedTrack.totalDuration).toDouble(),
          endTime: (text.trimEndTime - deletedTrack.totalDuration).toDouble(),
        ));
        continue;
      }
      // Otherwise, keep as is
      print(
          'Text track [${text.trimStartTime}, ${text.trimEndTime}] kept (default case)');
      newTextTracks.add(text);
    }
    _textTracks = newTextTracks;

    // Recalculate start/end times for all tracks (IDs are preserved)
    int currentTime = 0;
    for (var i = 0; i < _videoTracks.length; i++) {
      final t = _videoTracks[i];
      _videoTracks[i] = t.copyWith(
        startTime: currentTime,
        endTime: currentTime + t.totalDuration,
        id: t.id, // Explicitly preserve ID
      );
      currentTime += t.totalDuration;
    }
    _videoDuration =
        _videoTracks.fold(0, (sum, t) => sum + t.totalDuration).toDouble();

    if (_videoTracks.isEmpty) {
      _videoEditorController?.dispose();
      _videoEditorController = null;
      _videoDuration = 0.0;
      // Optionally, set a flag: noVideo = true;
      notifyListeners();
      return;
    }
    // Update the preview video to reflect the new track list
    await _updatePreviewVideo();
  }

  // Helper to update the preview video after track changes
  Future<void> _updatePreviewVideo() async {
    // Use combineMediaFiles to combine the current _videoTracks into a new preview file
    final processedFiles = _videoTracks.map((t) => t.processedFile).toList();
    final recSize = recommendedAspectRatio ?? const Size(1920, 1080);
    final (
      File? combined,
      List<(File, File)>? processedPairs,
    ) = await EditorVideoController.combineMediaFiles(
      processedFiles,
      outputHeight: recSize.height.toInt(),
      outputWidth: recSize.width.toInt(),
    );
    if (combined != null) {
      await initializeVideo(combined.path);
      await initializeOrResetControllers();
      _videoDuration =
          _videoEditorController?.videoDuration.inSeconds.toDouble() ?? 0.0;
      videoEditorController?.video.removeListener(listenVideoPosition);
      videoEditorController?.video.addListener(listenVideoPosition);
    }
    notifyListeners();
  }

  Future<void> pickAudioFile(BuildContext context) async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowMultiple: false,
      allowedExtensions: ['mp3'],
    );
    if (result?.files.single.path == null) return;

    final audioFileDuration =
        await _audioTracks.isNotEmpty ? _audioTracks.last.trimEndTime : 0.0;
    final remainAudioDuration = _videoDuration - audioFileDuration;

    if (remainAudioDuration < 1) {
      showSnackBar(
        context,
        "To add audio, there must be at least 1 second of space.",
      );
      return;
    }

    File pickedFile = File(result!.files.single.path!);
    double pickFileAudioDuration = (await getMediaDuration(
      pickedFile.path,
    ))
        .toDouble();
    Navigator.push<File>(
      context,
      MaterialPageRoute(
        builder: (context) {
          return AudioTrimmer(
            audioFile: pickedFile,
            audioDuration: pickFileAudioDuration,
            remainAudioDuration: remainAudioDuration,
          );
        },
      ),
    );
  }

  Future<void> addAudioTrack(File audioFile, double totalDuration) async {
    final double startTime =
        _audioTracks.isNotEmpty ? _audioTracks.last.trimEndTime : 0;

    final audioTrack = AudioTrackModel(
      audioFile: audioFile,
      trimStartTime: startTime,
      trimEndTime: startTime + totalDuration,
      totalDuration: totalDuration.toDouble(),
    );
    _audioTracks.add(audioTrack);
    initializeOrResetControllers();
    notifyListeners();
  }

  Future<void> removeAudioTrack(int index) async {
    _audioTracks.removeAt(index);
    notifyListeners();
  }

  Future<void> updateAudioTrack(
    int index,
    double startTime,
    double endTime,
  ) async {
    _audioTracks[index] = _audioTracks[index].copyWith(
      trimStartTime: startTime,
      trimEndTime: endTime,
    );
    notifyListeners();
  }

  // Video trim functionality
  Future<void> trimVideoTrack(
    int index,
    double startTime,
    double endTime,
  ) async {
    if (index < 0 || index >= _videoTracks.length) return;

    final track = _videoTracks[index];
    final trimDuration = endTime - startTime;

    print("=== Starting video trim ===");
    print("Track index: $index");
    print("Track ID: ${track.id}");
    print("Original file: ${track.processedFile.path}");
    print(
        "Start time: $startTime, End time: $endTime, Duration: $trimDuration");

    if (trimDuration <= 0) {
      throw Exception("Invalid trim duration: $trimDuration");
    }

    try {
      // Create trimmed video file using FFmpeg
      final trimmedFile = await _createTrimmedVideo(
        track.processedFile,
        startTime,
        endTime,
      );

      if (trimmedFile == null) {
        throw Exception("Failed to create trimmed video file");
      }

      print("Trimmed file created: ${trimmedFile.path}");

      // Update the track with new trim values and file
      final updatedTrack = track.copyWith(
        processedFile: trimmedFile,
        videoTrimStart: startTime,
        videoTrimEnd: endTime,
        totalDuration: trimDuration.toInt(),
        originalDuration: track.originalDuration == 0
            ? track.totalDuration.toDouble()
            : track.originalDuration,
      );

      _videoTracks[index] = updatedTrack;
      print(
          "Track updated: ${updatedTrack.id}, new duration: ${updatedTrack.totalDuration}");

      // Store original timeline positions before recalculation
      final originalSegmentStart = track.startTime.toDouble();
      final originalSegmentEnd = track.endTime.toDouble();
      final originalSegmentDuration = originalSegmentEnd - originalSegmentStart;

      // Recalculate timeline positions for all tracks
      await _recalculateVideoTrackPositions();

      // Get updated track with new timeline positions
      final recalculatedTrack = _videoTracks[index];
      final newSegmentStart = recalculatedTrack.startTime.toDouble();
      final newSegmentEnd = recalculatedTrack.endTime.toDouble();

      // Update preview video to reflect changes
      if (_videoTracks.length == 1) {
        // For single track, directly use the trimmed file
        print("Single track detected, initializing with trimmed file directly");
        await initializeVideo(trimmedFile.path);
        // Force refresh duration after single track initialization
        _videoDuration =
            _videoEditorController?.videoDuration.inSeconds.toDouble() ?? 0.0;
        print("Updated video duration after trim: $_videoDuration seconds");
        notifyListeners();
      } else {
        // For multiple tracks, combine them
        print("Multiple tracks detected, combining videos");
        await _updatePreviewVideo();
      }

      // Apply cascade updates to text and audio overlays using correct timeline positions
      final adjustmentMessages = await _cascadeUpdateAfterVideoTrim(
        index,
        originalSegmentStart,
        originalSegmentEnd,
        newSegmentStart,
      );
      print("Adjustment messages after trim:");
      adjustmentMessages.forEach(print);

      // Update the video editor controller
      await initializeOrResetControllers();
      _videoDuration =
          _videoEditorController?.videoDuration.inSeconds.toDouble() ?? 0.0;
      videoEditorController?.video.removeListener(listenVideoPosition);
      videoEditorController?.video.addListener(listenVideoPosition);

      notifyListeners();
    } catch (e) {
      print("Error during video trim: $e");
      rethrow;
    }
  }

  // Add a video track sequentially (stacking)
  Future<void> addVideoTrack(
    File originalFile,
    File processedFile,
    int totalDuration,
  ) async {
    int startTime = _videoTracks.isNotEmpty ? _videoTracks.last.endTime : 0;
    int endTime = startTime + totalDuration;

    // Check if this is an image-based video
    final isImageBased = _isImageFile(originalFile.path);

    final track = VideoTrackModel(
      originalFile: originalFile,
      processedFile: processedFile,
      startTime: startTime,
      endTime: endTime,
      totalDuration: totalDuration,
      isImageBased: isImageBased,
    );
    _videoTracks.add(track);
    _videoDuration =
        _videoTracks.fold(0, (sum, t) => sum + t.totalDuration).toDouble();

    if (_videoTracks.length == 1) {
      await initializeVideo(processedFile.path);
    }
    notifyListeners();
  }

  // Remove a video track and recalculate start/end times, preserving IDs
  Future<void> removeVideoTrack(int index) async {
    if (index < 0 || index >= _videoTracks.length) return;
    final deletedTrack = _videoTracks[index];

    // Remove the track at the given index
    _videoTracks.removeAt(index);

    // Remove mute state for this segment
    videoMuteStates.remove(deletedTrack.id);

    // --- AUDIO TRACKS ---
    List<AudioTrackModel> newAudioTracks = [];
    for (final audio in _audioTracks) {
      // Entirely before deleted segment: keep as is
      if (audio.trimEndTime <= deletedTrack.startTime) {
        print(
            'Audio track [${audio.trimStartTime}, ${audio.trimEndTime}] kept (before deleted segment)');
        newAudioTracks.add(audio);
        continue;
      }
      // Fully within deleted segment: remove
      if (audio.trimStartTime >= deletedTrack.startTime &&
          audio.trimEndTime <= deletedTrack.endTime) {
        print(
            'Audio track [${audio.trimStartTime}, ${audio.trimEndTime}] removed (fully within deleted segment)');
        continue;
      }
      // Spans before and after deleted segment: split into two
      if (audio.trimStartTime < deletedTrack.startTime &&
          audio.trimEndTime > deletedTrack.endTime) {
        print(
            'Audio track [${audio.trimStartTime}, ${audio.trimEndTime}] split (spans deleted segment)');
        newAudioTracks.add(audio.copyWith(
          id: const Uuid().v4(),
          trimStartTime: audio.trimStartTime,
          trimEndTime: deletedTrack.startTime.toDouble(),
        ));
        newAudioTracks.add(audio.copyWith(
          id: const Uuid().v4(),
          trimStartTime:
              (deletedTrack.endTime - deletedTrack.totalDuration).toDouble(),
          trimEndTime:
              (audio.trimEndTime - deletedTrack.totalDuration).toDouble(),
        ));
        continue;
      }
      // Overlaps start of deleted segment
      if (audio.trimStartTime < deletedTrack.startTime &&
          audio.trimEndTime > deletedTrack.startTime &&
          audio.trimEndTime <= deletedTrack.endTime) {
        print('Audio track overlaps start of deleted segment');
        // Adjust audio track timing here if needed
      }
    }

    print("=== Video trim completed successfully ===");

    // Force UI update to ensure VideoTrack widgets are rebuilt with new file
    notifyListeners();

    // Add a small delay to ensure the UI has updated before any additional processing
    await Future.delayed(const Duration(milliseconds: 100));
  }
}

Future<File?> _createTrimmedVideo(
  File inputFile,
  double startTime,
  double endTime,
) async {
  try {
    final tempDir = await getTemporaryDirectory();
    final outputPath =
        '${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}_trimmed_video.mp4';

    final duration = endTime - startTime;

    // Use re-encoding for accurate trimming instead of stream copy
    // This ensures proper frame accuracy and playback
    final command =
        '-i "${inputFile.path}" -ss $startTime -t $duration -c:v libx264 -c:a aac -preset fast -crf 23 "$outputPath"';

    print("Trimming video with command: $command");

    final session = await FFmpegKit.execute(command);
    final returnCode = await session.getReturnCode();
    final logs = await session.getAllLogsAsString();

    print("FFmpeg trim return code: $returnCode");
    print("FFmpeg trim logs: $logs");

    if (ReturnCode.isSuccess(returnCode) && File(outputPath).existsSync()) {
      final outputFile = File(outputPath);
      final fileSize = await outputFile.length();
      print(
          "Trimmed video created successfully: $outputPath (${fileSize} bytes)");

      // Validate the trimmed video
      final isValid =
          await _validateTrimmedVideo(outputFile, endTime - startTime);
      if (!isValid) {
        print("Trimmed video validation failed");
        return null;
      }

      return outputFile;
    } else {
      print("Failed to create trimmed video. Return code: $returnCode");
      print("Output file exists: ${File(outputPath).existsSync()}");
      return null;
    }
  } catch (e) {
    print("Error creating trimmed video: $e");
    return null;
  }
}

Future<void> _recalculateVideoTrackPositions() async {
  int currentTime = 0;
  for (var i = 0; i < _videoTracks.length; i++) {
    final track = _videoTracks[i];
    _videoTracks[i] = track.copyWith(
      startTime: currentTime,
      endTime: currentTime + track.totalDuration,
    );
    currentTime += track.totalDuration;
  }

  _videoDuration =
      _videoTracks.fold(0, (sum, t) => sum + t.totalDuration).toDouble();
}

Future<bool> _validateTrimmedVideo(
    File videoFile, double expectedDuration) async {
  try {
    // Use FFprobe to get video information
    final session =
        await FFmpegKit.execute('-i "${videoFile.path}" -hide_banner');
    final logs = await session.getAllLogsAsString() ?? '';

    // Check if video has valid duration
    final durationRegex = RegExp(r'Duration: (\d{2}):(\d{2}):(\d{2})\.(\d{2})');
    final match = durationRegex.firstMatch(logs);

    if (match != null) {
      final hours = int.parse(match.group(1)!);
      final minutes = int.parse(match.group(2)!);
      final seconds = int.parse(match.group(3)!);
      final centiseconds = int.parse(match.group(4)!);

      final actualDuration =
          hours * 3600 + minutes * 60 + seconds + centiseconds / 100;
      print(
          "Expected duration: $expectedDuration, Actual duration: $actualDuration");

      // Allow some tolerance (±0.5 seconds)
      final tolerance = 0.5;
      final isValid = (actualDuration - expectedDuration).abs() <= tolerance &&
          actualDuration > 0;

      if (!isValid) {
        print(
            "Duration mismatch: expected $expectedDuration, got $actualDuration");
      }

      return isValid;
    } else {
      print("Could not parse video duration from logs");
      return false;
    }
  } catch (e) {
    print("Error validating trimmed video: $e");
    return false;
  }
}

// Helper method to check if a file is an image
bool _isImageFile(String filePath) {
  final imageExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif', '.bmp'];
  final extension = filePath.toLowerCase().split('.').last;
  return imageExtensions.contains('.$extension');
}

// Image video stretch functionality
Future<void> stretchImageVideo(int index, double newDuration) async {
  if (index < 0 || index >= _videoTracks.length) return;

  final track = _videoTracks[index];

  // Only allow stretching for image-based videos
  if (!track.isImageBased) {
    throw Exception("Stretch is only available for image-based videos");
  }

  print("=== Starting image video stretch ===");
  print("Track index: $index");
  print("Track ID: ${track.id}");
  print("Original file: ${track.originalFile.path}");
  print("Current duration: ${track.totalDuration}, New duration: $newDuration");

  if (newDuration <= 0) {
    throw Exception("Invalid stretch duration: $newDuration");
  }

  try {
    // Add to undo stack before making changes
    _addToUndoStack(
      EditOperation(
        EditOperationType.stretch,
        {
          'trackIndex': index,
          'originalDuration': track.totalDuration.toDouble(),
          'originalFile': track.processedFile,
        },
        {
          'trackIndex': index,
          'newDuration': newDuration,
        },
      ),
    );

    // Create stretched video file using FFmpeg
    final stretchedFile = await _createStretchedImageVideo(
      track.originalFile,
      newDuration,
      _getAspectRatioDimensions(),
    );

    if (stretchedFile == null) {
      throw Exception("Failed to create stretched video file");
    }

    print("Stretched file created: ${stretchedFile.path}");

    // Store original timeline positions for cascade updates
    final originalSegmentStart = track.startTime.toDouble();
    final originalSegmentEnd = track.endTime.toDouble();
    final originalDuration = track.totalDuration.toDouble();

    // Update the track with new duration and file
    final updatedTrack = track.copyWith(
      processedFile: stretchedFile,
      totalDuration: newDuration.toInt(),
      customDuration: newDuration,
      lastModified: DateTime.now(),
    );

    _videoTracks[index] = updatedTrack;
    print(
        "Track updated: ${updatedTrack.id}, new duration: ${updatedTrack.totalDuration}");

    // Recalculate timeline positions for all tracks
    await _recalculateVideoTrackPositions();

    // Get new timeline positions after recalculation
    final newSegmentStart = _videoTracks[index].startTime.toDouble();
    final newSegmentEnd = _videoTracks[index].endTime.toDouble();

    // Update preview video to reflect changes
    if (_videoTracks.length == 1) {
      // For single track, directly use the stretched file
      print("Single track detected, initializing with stretched file directly");
      await initializeVideo(stretchedFile.path);
    } else {
      // For multiple tracks, combine them
      print("Multiple tracks detected, combining videos");
      await _updatePreviewVideo();
    }

    // Apply cascade updates to text and audio overlays
    final adjustmentMessages = await _cascadeUpdateAfterStretch(
      index,
      originalSegmentStart,
      originalSegmentEnd,
      newSegmentStart,
      newSegmentEnd,
      originalDuration,
      newDuration,
    );

    print("=== Image video stretch completed successfully ===");

    // Force UI update
    notifyListeners();

    // Add a small delay to ensure the UI has updated
    await Future.delayed(const Duration(milliseconds: 100));

    // Return adjustment messages for user feedback (if needed)
    if (adjustmentMessages.isNotEmpty) {
      print("Overlay adjustments made: ${adjustmentMessages.join(', ')}");
    }
  } catch (e) {
    print("=== Image video stretch failed: ${e.toString()} ===");
    throw Exception("Failed to stretch image video: ${e.toString()}");
  }
}

Future<File?> _createStretchedImageVideo(
  File originalImageFile,
  double newDuration,
  Size targetSize,
) async {
  try {
    final tempDir = await getTemporaryDirectory();
    final outputPath =
        '${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}_stretched_img.mp4';

    // FFmpeg command to create video with new duration
    final command = '-loop 1 -t $newDuration -i "${originalImageFile.path}" '
        '-vf "scale=${targetSize.width.toInt()}:${targetSize.height.toInt()}:force_original_aspect_ratio=decrease,'
        'pad=${targetSize.width.toInt()}:${targetSize.height.toInt()}:(ow-iw)/2:(oh-ih)/2:color=black" '
        '-c:v h264 -preset medium -crf 23 -pix_fmt yuv420p '
        '-r 30 "$outputPath"';

    print("Stretch FFmpeg command: $command");

    final session = await FFmpegKit.execute(command);
    final returnCode = await session.getReturnCode();
    final logs = await session.getLogsAsString();

    if (ReturnCode.isSuccess(returnCode) && File(outputPath).existsSync()) {
      final outputFile = File(outputPath);
      final fileSize = await outputFile.length();
      print(
          "Stretched video created successfully: $outputPath (${fileSize} bytes)");

      // Validate the stretched video
      final isValid = await _validateStretchedVideo(outputFile, newDuration);
      if (!isValid) {
        print("Stretched video validation failed");
        return null;
      }

      return outputFile;
    } else {
      print("Failed to create stretched video. Return code: $returnCode");
      print("FFmpeg logs: $logs");
      return null;
    }
  } catch (e) {
    print("Error creating stretched video: $e");
    return null;
  }
}

Future<bool> _validateStretchedVideo(
    File videoFile, double expectedDuration) async {
  try {
    // Use FFprobe to get video information
    final session =
        await FFmpegKit.execute('-i "${videoFile.path}" -hide_banner');
    final logs = await session.getAllLogsAsString() ?? '';

    // Check if video has valid duration
    final durationRegex = RegExp(r'Duration: (\d{2}):(\d{2}):(\d{2})\.(\d{2})');
    final match = durationRegex.firstMatch(logs);

    if (match != null) {
      final hours = int.parse(match.group(1)!);
      final minutes = int.parse(match.group(2)!);
      final seconds = int.parse(match.group(3)!);
      final centiseconds = int.parse(match.group(4)!);

      final actualDuration =
          hours * 3600 + minutes * 60 + seconds + centiseconds / 100;
      print(
          "Expected duration: $expectedDuration, Actual duration: $actualDuration");

      // Allow some tolerance (±0.5 seconds)
      final tolerance = 0.5;
      final isValid = (actualDuration - expectedDuration).abs() <= tolerance &&
          actualDuration > 0;

      if (!isValid) {
        print(
            "Duration validation failed: expected $expectedDuration, got $actualDuration");
      }

      return isValid;
    } else {
      print("Could not parse video duration from logs");
      return false;
    }
  } catch (e) {
    print("Error validating stretched video: $e");
    return false;
  }
}

// Cascade update system for handling overlays when video is stretched
Future<List<String>> _cascadeUpdateAfterStretch(
  int videoTrackIndex,
  double originalSegmentStart,
  double originalSegmentEnd,
  double newSegmentStart,
  double newSegmentEnd,
  double originalDuration,
  double newDuration,
) async {
  // Validation: Check video track index
  if (videoTrackIndex < 0 || videoTrackIndex >= _videoTracks.length) {
    print(
        "ERROR: Invalid video track index: $videoTrackIndex (total: ${_videoTracks.length})");
    return [];
  }

  // Validation: Check duration parameters
  if (originalDuration <= 0 || newDuration <= 0) {
    print(
        "ERROR: Invalid duration parameters: original=$originalDuration, new=$newDuration");
    return [];
  }

  final durationChange = newDuration - originalDuration;
  final adjustmentMessages = <String>[];

  print("=== Starting cascade updates after stretch ===");
  print("Video track index: $videoTrackIndex");
  print(
      "Original segment: $originalSegmentStart - $originalSegmentEnd (${originalDuration}s)");
  print("New segment: $newSegmentStart - $newSegmentEnd (${newDuration}s)");
  print("Duration change: ${durationChange}s");

  // Update text tracks that come after this video segment
  final textMessages = await _updateTextTracksForSegmentStretch(
    originalSegmentStart,
    originalSegmentEnd,
    newSegmentStart,
    newSegmentEnd,
    durationChange,
  );
  adjustmentMessages.addAll(textMessages);

  // Update audio tracks that come after this video segment
  final audioMessages = await _updateAudioTracksForSegmentStretch(
    originalSegmentStart,
    originalSegmentEnd,
    newSegmentStart,
    newSegmentEnd,
    durationChange,
  );
  adjustmentMessages.addAll(audioMessages);

  print("=== Cascade updates completed ===");
  print("Adjustments made: ${adjustmentMessages.length}");

  return adjustmentMessages;
}

Future<List<String>> _updateTextTracksForSegmentStretch(
  double originalSegmentStart,
  double originalSegmentEnd,
  double newSegmentStart,
  double newSegmentEnd,
  double durationChange,
) async {
  final adjustmentMessages = <String>[];

  for (int i = 0; i < _textTracks.length; i++) {
    final textTrack = _textTracks[i];
    final textStart = textTrack.trimStartTime;
    final textEnd = textTrack.trimEndTime;

    // Only adjust text tracks that start after the stretched segment
    if (textStart >= originalSegmentEnd) {
      final newStartTime = textStart + durationChange;
      final newEndTime = textEnd + durationChange;

      final updatedTextTrack = textTrack.copyWith(
        startTime: newStartTime,
        endTime: newEndTime,
      );

      _textTracks[i] = updatedTextTrack;
      adjustmentMessages.add(
          "Text track ${i + 1} shifted by ${durationChange.toStringAsFixed(1)}s");

      print(
          "Text track $i adjusted: ${textStart}s-${textEnd}s → ${newStartTime}s-${newEndTime}s");
    }
  }

  return adjustmentMessages;
}

Future<List<String>> _updateAudioTracksForSegmentStretch(
  double originalSegmentStart,
  double originalSegmentEnd,
  double newSegmentStart,
  double newSegmentEnd,
  double durationChange,
) async {
  final adjustmentMessages = <String>[];

  for (int i = 0; i < _audioTracks.length; i++) {
    final audioTrack = _audioTracks[i];
    final audioStart = audioTrack.trimStartTime;
    final audioEnd = audioTrack.trimEndTime;

    // Only adjust audio tracks that start after the stretched segment
    if (audioStart >= originalSegmentEnd) {
      final newStartTime = audioStart + durationChange;
      final newEndTime = audioEnd + durationChange;

      final updatedAudioTrack = audioTrack.copyWith(
        trimStartTime: newStartTime,
        trimEndTime: newEndTime,
      );

      _audioTracks[i] = updatedAudioTrack;
      adjustmentMessages.add(
          "Audio track ${i + 1} shifted by ${durationChange.toStringAsFixed(1)}s");

      print(
          "Audio track $i adjusted: ${audioStart}s-${audioEnd}s → ${newStartTime}s-${newEndTime}s");
    }
  }

  return adjustmentMessages;
}

// Cascade update system for handling overlays when video is trimmed
Future<List<String>> _cascadeUpdateAfterVideoTrim(
  int videoTrackIndex,
  double originalSegmentStart,
  double originalSegmentEnd,
  double newSegmentStart,
  double newSegmentEnd,
  double trimStart,
  double trimEnd,
) async {
  // Validation: Check video track index
  if (videoTrackIndex < 0 || videoTrackIndex >= _videoTracks.length) {
    print(
        "ERROR: Invalid video track index: $videoTrackIndex (total: ${_videoTracks.length})");
    return [];
  }

  // Validation: Check trim parameters
  if (trimStart < 0 || trimEnd <= trimStart) {
    print("ERROR: Invalid trim parameters: start=$trimStart, end=$trimEnd");
    return [];
  }

  final trimOffset = trimStart;
  final newSegmentDuration = trimEnd - trimStart;
  final originalSegmentDuration = originalSegmentEnd - originalSegmentStart;
  final durationChange = newSegmentDuration - originalSegmentDuration;
  final adjustmentMessages = <String>[];

  // Validation: Check segment duration
  if (newSegmentDuration <= 0) {
    print(
        "ERROR: New segment duration is zero or negative: $newSegmentDuration");
    return [];
  }

  // Validation: Check segment bounds
  if (originalSegmentStart >= originalSegmentEnd) {
    print(
        "ERROR: Invalid original segment bounds: start=$originalSegmentStart, end=$originalSegmentEnd");
    return [];
  }

  print("=== Starting segment-aware cascade updates ===");
  print(
      "Video segment $videoTrackIndex: ${originalSegmentStart}s - ${originalSegmentEnd}s (original)");
  print("New timeline position: ${newSegmentStart}s - ${newSegmentEnd}s");
  print("Trim: ${trimStart}s - ${trimEnd}s (duration: ${newSegmentDuration}s)");
  print("Duration change: ${durationChange}s");
  print("Total video segments: ${_videoTracks.length}");
  print("Total text overlays: ${_textTracks.length}");
  print("Total audio tracks: ${_audioTracks.length}");

  // Log all video segments for context
  for (int i = 0; i < _videoTracks.length; i++) {
    final track = _videoTracks[i];
    print(
        "  Segment $i: ${track.startTime}s - ${track.endTime}s (duration: ${track.totalDuration}s)");
  }

  // Log all overlays for context
  for (int i = 0; i < _textTracks.length; i++) {
    final overlay = _textTracks[i];
    print(
        "  Text overlay $i: '${overlay.text}' at ${overlay.trimStartTime}s - ${overlay.trimEndTime}s");
  }

  for (int i = 0; i < _audioTracks.length; i++) {
    final track = _audioTracks[i];
    print("  Audio track $i: ${track.trimStartTime}s - ${track.trimEndTime}s");
  }

  // Update text overlays that intersect with this video segment
  final textMessages = await _updateTextOverlaysForSegmentTrim(
    originalSegmentStart,
    originalSegmentEnd,
    newSegmentStart,
    newSegmentEnd,
    trimOffset,
    newSegmentDuration,
    durationChange,
  );
  adjustmentMessages.addAll(textMessages);

  // Update audio tracks that intersect with this video segment
  final audioMessages = await _updateAudioTracksForSegmentTrim(
    originalSegmentStart,
    originalSegmentEnd,
    newSegmentStart,
    newSegmentEnd,
    trimOffset,
    newSegmentDuration,
    durationChange,
  );
  adjustmentMessages.addAll(audioMessages);

  print("=== Cascade updates completed ===");
  print("Adjustments made: ${adjustmentMessages.length}");

  return adjustmentMessages;
}

Future<List<String>> _updateTextOverlaysForSegmentTrim(
  double originalSegmentStart,
  double originalSegmentEnd,
  double newSegmentStart,
  double newSegmentEnd,
  double trimOffset,
  double newSegmentDuration,
  double durationChange,
) async {
  final adjustmentMessages = <String>[];

  print("Updating ${_textTracks.length} text overlays for segment trim");

  for (int i = _textTracks.length - 1; i >= 0; i--) {
    final textTrack = _textTracks[i];

    print(
        "Text overlay $i: ${textTrack.trimStartTime}s - ${textTrack.trimEndTime}s");

    // Check if this text overlay intersects with the original video segment
    final overlayStart = textTrack.trimStartTime;
    final overlayEnd = textTrack.trimEndTime;

    // Only process overlays that intersect with this video segment
    if (overlayEnd <= originalSegmentStart ||
        overlayStart >= originalSegmentEnd) {
      print("Text overlay $i: No intersection with segment, skipping");
      continue; // This overlay doesn't intersect with the trimmed segment
    }

    print("Text overlay $i: Intersects with segment, processing...");

    // Simplified logic based on overlay position relative to segment
    if (overlayStart >= originalSegmentEnd) {
      // Case 1: Overlay starts after this segment - shift by duration change
      print("Text overlay $i: After segment - shifting by ${durationChange}s");
      _textTracks[i] = textTrack.copyWith(
        startTime: overlayStart + durationChange,
        endTime: overlayEnd + durationChange,
        updateTimestamp: true,
      );
      adjustmentMessages.add(
          "Shifted text overlay '${textTrack.text}' by ${durationChange}s");
    } else if (overlayEnd <= originalSegmentStart) {
      // Case 2: Overlay ends before this segment - no change needed
      print("Text overlay $i: Before segment - no adjustment needed");
      continue;
    } else if (overlayStart >= originalSegmentStart &&
        overlayEnd <= originalSegmentEnd) {
      // Case 3: Overlay completely within segment - adjust relative to trim
      print("Text overlay $i: Within segment - adjusting for trim");

      final overlayStartInSegment = overlayStart - originalSegmentStart;
      final overlayEndInSegment = overlayEnd - originalSegmentStart;

      // Apply trim offset
      final newStartInSegment =
          (overlayStartInSegment - trimOffset).clamp(0.0, newSegmentDuration);
      final newEndInSegment = (overlayEndInSegment - trimOffset)
          .clamp(newStartInSegment, newSegmentDuration);

      // Convert back to timeline positions (using new segment position)
      final newTimelineStart = newSegmentStart + newStartInSegment;
      final newTimelineEnd = newSegmentStart + newEndInSegment;

      if (newEndInSegment <= 0 || newStartInSegment >= newSegmentDuration) {
        // Overlay is outside trim range - remove it
        _textTracks.removeAt(i);
        adjustmentMessages.add(
            "Removed text overlay '${textTrack.text}' (outside trim range)");
        print("Removed text overlay $i: outside trim range");
      } else {
        // Update positions
        _textTracks[i] = textTrack.copyWith(
          startTime: newTimelineStart,
          endTime: newTimelineEnd,
          updateTimestamp: true,
        );
        adjustmentMessages
            .add("Adjusted text overlay '${textTrack.text}' for trim");
        print(
            "Updated text overlay $i: ${newTimelineStart}s - ${newTimelineEnd}s");
      }
    } else {
      // Case 4: Overlay spans multiple segments - partial adjustment
      print("Text overlay $i: Spans segments - applying partial adjustment");

      if (overlayStart < originalSegmentStart &&
          overlayEnd > originalSegmentEnd) {
        // Overlay spans this entire segment - adjust for duration change
        final newOverlayEnd = overlayEnd + durationChange;
        _textTracks[i] = textTrack.copyWith(
          startTime: overlayStart, // Keep original start
          endTime: newOverlayEnd,
          updateTimestamp: true,
        );
        adjustmentMessages.add(
            "Partially adjusted text overlay '${textTrack.text}' (spans segment)");
        print(
            "Partially adjusted text overlay $i: ${overlayStart}s - ${newOverlayEnd}s");
      } else if (overlayStart < originalSegmentStart) {
        // Overlay starts before segment and ends within it - adjust end
        final overlayEndInSegment = overlayEnd - originalSegmentStart;
        final newEndInSegment =
            (overlayEndInSegment - trimOffset).clamp(0.0, newSegmentDuration);
        final newTimelineEnd = newSegmentStart + newEndInSegment;

        _textTracks[i] = textTrack.copyWith(
          startTime: overlayStart, // Keep original start
          endTime: newTimelineEnd,
          updateTimestamp: true,
        );
        adjustmentMessages
            .add("Adjusted end of text overlay '${textTrack.text}'");
        print("Adjusted overlay end $i: ${overlayStart}s - ${newTimelineEnd}s");
      } else {
        // Overlay starts within segment and ends after it - adjust start and shift
        final overlayStartInSegment = overlayStart - originalSegmentStart;
        final newStartInSegment =
            (overlayStartInSegment - trimOffset).clamp(0.0, newSegmentDuration);
        final newTimelineStart = newSegmentStart + newStartInSegment;
        final newTimelineEnd = overlayEnd + durationChange;

        _textTracks[i] = textTrack.copyWith(
          startTime: newTimelineStart,
          endTime: newTimelineEnd,
        );
      }
    }
  }
}

// Individual clip editing methods
void updateClipCrop(String clipId, CropModel cropModel) {
  final index = _videoTracks.indexWhere((track) => track.id == clipId);
  if (index == -1) return;

  _addToUndoStack(EditOperation(
    EditOperationType.crop,
    _videoTracks[index].cropModel,
    cropModel,
    clipId: clipId,
  ));

  _videoTracks[index] = _videoTracks[index].copyWith(cropModel: cropModel);

  // Only update preview if this is the currently selected clip
  if (_selectedClipId == clipId) {
    _updatePreviewForClip(clipId);
  }

  notifyListeners();
}

void updateClipRotation(String clipId, int rotation) {
  final index = _videoTracks.indexWhere((track) => track.id == clipId);
  if (index == -1) return;

  _addToUndoStack(EditOperation(
    EditOperationType.rotation,
    _videoTracks[index].rotation,
    rotation,
    clipId: clipId,
  ));

  _videoTracks[index] = _videoTracks[index].copyWith(rotation: rotation);

  if (_selectedClipId == clipId) {
    _updatePreviewForClip(clipId);
  }

  notifyListeners();
}

void updateClipFilter(String clipId, String filter) {
  final index = _videoTracks.indexWhere((track) => track.id == clipId);
  if (index == -1) return;

  _addToUndoStack(EditOperation(
    EditOperationType.filter,
    _videoTracks[index].filter,
    filter,
    clipId: clipId,
  ));

  _videoTracks[index] = _videoTracks[index].copyWith(filter: filter);

  if (_selectedClipId == clipId) {
    _updatePreviewForClip(clipId);
  }

  notifyListeners();
}

// Apply crop to specific clip only
Future<void> applyCropToClip(String clipId) async {
  final track = _videoTracks.firstWhere((t) => t.id == clipId);
  if (track.cropModel == null || !track.cropModel!.enabled) return;

  final tempDir = await getTemporaryDirectory();
  final outputPath =
      '${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}_cropped_${clipId}.mp4';

  final cropFilter = track.cropModel!.toFFmpegFilter();
  final command =
      '-i ${track.processedFile.path} -filter:v "$cropFilter" -c:a copy $outputPath';

  final session = await FFmpegKit.execute(command);
  final returnCode = await session.getReturnCode();

  if (ReturnCode.isSuccess(returnCode) && File(outputPath).existsSync()) {
    // Update only this specific clip
    final index = _videoTracks.indexWhere((t) => t.id == clipId);
    _videoTracks[index] = _videoTracks[index].copyWith(
      processedFile: File(outputPath),
      cropModel: track.cropModel!
          .copyWith(enabled: false), // Reset crop after applying
    );

    // Update preview only if this is the selected clip
    if (_selectedClipId == clipId) {
      await _updatePreviewForClip(clipId);
    }

    notifyListeners();
  }
}

// Update preview for specific clip without affecting others
Future<void> _updatePreviewForClip(String clipId) async {
  final track = _videoTracks.firstWhere((t) => t.id == clipId);

  // If this is a single clip project, update main preview
  if (_videoTracks.length == 1) {
    await initializeVideo(track.processedFile.path);
  } else {
    // For multi-clip projects, regenerate combined preview
    await _updatePreviewVideo();
  }
}

Future<List<String>> _updateAudioTracksForSegmentTrim(
  double originalSegmentStart,
  double originalSegmentEnd,
  double newSegmentStart,
  double newSegmentEnd,
  double trimOffset,
  double newSegmentDuration,
  double durationChange,
) async {
  final adjustmentMessages = <String>[];

  print("Updating ${_audioTracks.length} audio tracks for segment trim");

  for (int i = _audioTracks.length - 1; i >= 0; i--) {
    final audioTrack = _audioTracks[i];

    print(
        "Audio track $i: ${audioTrack.trimStartTime}s - ${audioTrack.trimEndTime}s");

    // Check if this audio track intersects with the original video segment
    final trackStart = audioTrack.trimStartTime;
    final trackEnd = audioTrack.trimEndTime;

    // Only process tracks that intersect with this video segment
    if (trackEnd <= originalSegmentStart || trackStart >= originalSegmentEnd) {
      print("Audio track $i: No intersection with segment, skipping");
      continue; // This track doesn't intersect with the trimmed segment
    }

    print("Audio track $i: Intersects with segment, processing...");

    // Simplified logic based on track position relative to segment
    if (trackStart >= originalSegmentEnd) {
      // Case 1: Track starts after this segment - shift by duration change
      print("Audio track $i: After segment - shifting by ${durationChange}s");
      _audioTracks[i] = audioTrack.copyWith(
        trimStartTime: trackStart + durationChange,
        trimEndTime: trackEnd + durationChange,
        updateTimestamp: true,
      );
      adjustmentMessages.add("Shifted audio track by ${durationChange}s");
    } else if (trackEnd <= originalSegmentStart) {
      // Case 2: Track ends before this segment - no change needed
      print("Audio track $i: Before segment - no adjustment needed");
      continue;
    } else if (trackStart >= originalSegmentStart &&
        trackEnd <= originalSegmentEnd) {
      // Case 3: Track completely within segment - adjust relative to trim
      print("Audio track $i: Within segment - adjusting for trim");

      final trackStartInSegment = trackStart - originalSegmentStart;
      final trackEndInSegment = trackEnd - originalSegmentStart;

      // Apply trim offset
      final newStartInSegment =
          (trackStartInSegment - trimOffset).clamp(0.0, newSegmentDuration);
      final newEndInSegment = (trackEndInSegment - trimOffset)
          .clamp(newStartInSegment, newSegmentDuration);

      // Convert back to timeline positions (using new segment position)
      final newTimelineStart = newSegmentStart + newStartInSegment;
      final newTimelineEnd = newSegmentStart + newEndInSegment;

      if (newEndInSegment <= 0 || newStartInSegment >= newSegmentDuration) {
        // Track is outside trim range - remove it
        _audioTracks.removeAt(i);
        adjustmentMessages.add("Removed audio track (outside trim range)");
        print("Removed audio track $i: outside trim range");
      } else {
        // Update positions
        _audioTracks[i] = audioTrack.copyWith(
          trimStartTime: newTimelineStart,
          trimEndTime: newTimelineEnd,
          totalDuration: newTimelineEnd - newTimelineStart,
          updateTimestamp: true,
        );
        adjustmentMessages.add("Adjusted audio track for trim");
        print(
            "Updated audio track $i: ${newTimelineStart}s - ${newTimelineEnd}s");
      }
    } else {
      // Case 4: Track spans multiple segments - partial adjustment
      print("Audio track $i: Spans segments - applying partial adjustment");

      if (trackStart < originalSegmentStart && trackEnd > originalSegmentEnd) {
        // Track spans this entire segment - adjust for duration change
        final newTrackEnd = trackEnd + durationChange;
        _audioTracks[i] = audioTrack.copyWith(
          trimStartTime: trackStart, // Keep original start
          trimEndTime: newTrackEnd,
          totalDuration: newTrackEnd - trackStart,
          updateTimestamp: true,
        );
        adjustmentMessages
            .add("Partially adjusted audio track (spans segment)");
        print(
            "Partially adjusted audio track $i: ${trackStart}s - ${newTrackEnd}s");
      } else if (trackStart < originalSegmentStart) {
        // Track starts before segment and ends within it - adjust end
        final trackEndInSegment = trackEnd - originalSegmentStart;
        final newEndInSegment =
            (trackEndInSegment - trimOffset).clamp(0.0, newSegmentDuration);
        final newTimelineEnd = newSegmentStart + newEndInSegment;

        _audioTracks[i] = audioTrack.copyWith(
          trimStartTime: trackStart, // Keep original start
          trimEndTime: newTimelineEnd,
          totalDuration: newTimelineEnd - trackStart,
          updateTimestamp: true,
        );
        adjustmentMessages.add("Adjusted end of audio track");
        print("Adjusted track end $i: ${trackStart}s - ${newTimelineEnd}s");
      } else {
        // Track starts within segment and ends after it - adjust start and shift
        final trackStartInSegment = trackStart - originalSegmentStart;
        final newStartInSegment =
            (trackStartInSegment - trimOffset).clamp(0.0, newSegmentDuration);
        final newTimelineStart = newSegmentStart + newStartInSegment;
        final newTimelineEnd = trackEnd + durationChange;

        _audioTracks[i] = audioTrack.copyWith(
          trimStartTime: newTimelineStart,
          trimEndTime: newTimelineEnd,
          totalDuration: newTimelineEnd - newTimelineStart,
          updateTimestamp: true,
        );
        adjustmentMessages.add("Adjusted start and shifted audio track");
        print(
            "Adjusted track start+shift $i: ${newTimelineStart}s - ${newTimelineEnd}s");
      }
    }
  }

  return adjustmentMessages;
}

Future<void> addText(BuildContext context) async {
  _textFieldVisibility = false;
  _sendButtonVisibility = false;
  final text = _textEditingController.text.trim();
  _textEditingController.clear();
  if (text.isEmpty) return;

  final textDuration =
      await _textTracks.isNotEmpty ? _textTracks.last.trimEndTime : 0.0;
  final remainTextDuration = _videoDuration - textDuration;

  if (remainTextDuration < 1) {
    showSnackBar(
      context,
      "To add Text, there must be at least 1 second of space.",
    );
    return;
  }
  addTextTrack(text, remainTextDuration < 3 ? remainTextDuration : 3);
}

Future<void> addTextTrack(String text, totalDuration) async {
  // Remove automatic selection - user needs to explicitly click trim to show boundaries
  // if (_textTracks.isEmpty) setTextTrackIndex(0);
  final double startTime =
      _textTracks.isNotEmpty ? _textTracks.last.trimEndTime : 0;

  final textTrack = TextTrackModel(
    text: text,
    trimStartTime: startTime,
    trimEndTime: startTime + totalDuration,
  );

  _textTracks.add(textTrack);
  initializeOrResetControllers();
  notifyListeners();
}

Future<void> removeTextTrack(int index) async {
  _textTracks.removeAt(index);
  if (_textTracks.isEmpty) _layeredTextOnVideo = '';
  notifyListeners();
}

Future<void> updateTextTrack(
  int index,
  double startTime,
  double endTime,
) async {
  _textTracks[index] = _textTracks[index].copyWith(
    startTime: startTime,
    endTime: endTime,
  );
  notifyListeners();
}

Future<void> updateTextTrackModel(
  int index,
  TextTrackModel updatedTrack,
) async {
  _textTracks[index] = updatedTrack;
  notifyListeners();
}

void toggleAudioMute(String audioId) {
  audioMuteStates[audioId] = !(audioMuteStates[audioId] ?? false);
  notifyListeners();
}

bool isAudioMuted(String audioId) {
  return audioMuteStates[audioId] ?? false;
}

// Add clip selection state
String? _selectedClipId;
String? get selectedClipId => _selectedClipId;

void selectClip(String clipId) {
  if (_selectedClipId != clipId) {
    _selectedClipId = clipId;

    // Load the selected clip for preview/editing
    try {
      final selectedTrack = _videoTracks.firstWhere((t) => t.id == clipId);
      _loadClipForEditing(selectedTrack);
    } catch (e) {
      // Clip might be in other track types, handle gracefully
      print('Clip $clipId not found in video tracks: $e');
    }

    notifyListeners();
  }
}

void deselectClip() {
  _selectedClipId = null;
  notifyListeners();
}

// Add methods to update individual tracks
void updateVideoTrack(int index, VideoTrackModel updatedTrack) {
  if (index >= 0 && index < _videoTracks.length) {
    _videoTracks[index] = updatedTrack;
    notifyListeners();
  }
}

void updateAudioTrackModel(int index, AudioTrackModel updatedTrack) {
  if (index >= 0 && index < _audioTracks.length) {
    _audioTracks[index] = updatedTrack;
    notifyListeners();
  }
}

void updateTextTrackModelByIndex(int index, TextTrackModel updatedTrack) {
  if (index >= 0 && index < _textTracks.length) {
    _textTracks[index] = updatedTrack;
    notifyListeners();
  }
}

void updateOverlayTrack(int index, OverlayVideoTrackModel updatedTrack) {
  if (index >= 0 && index < _overlayVideoTracks.length) {
    _overlayVideoTracks[index] = updatedTrack;
    notifyListeners();
  }
}

Future<void> _loadClipForEditing(VideoTrackModel track) async {
  // Initialize video controller with the selected clip
  await initializeVideo(track.processedFile.path);

  // Load clip-specific settings
  if (track.cropModel != null) {
    _cropRect = track.cropModel!.toRect();
  }
  _rotation = track.rotation;
  _currentFilter = track.filter ?? 'None';
  _playbackSpeed = track.playbackSpeed;
}

// Get currently selected clip
VideoTrackModel? get selectedClip {
  if (_selectedClipId == null) return null;
  try {
    return _videoTracks.firstWhere((t) => t.id == _selectedClipId);
  } catch (e) {
    return null;
  }
}

// Cleanup
@override
void dispose() {
  // _videoEditorController?.removeListener(_onVideoPositionChanged);
  _videoEditorController?.removeListener(listenVideoPosition);
  _positionTimer?.cancel();
  _videoEditorController?.dispose();
  _audioController?.dispose();
  _videoScrollController?.dispose();
  _audioScrollController?.dispose();
  _textScrollController?.dispose();
  _textEditingController.dispose();
  super.dispose();
}

// Helper to read debug logs from file
Future<String> _readLogs() async {
  try {
    final dir = await getApplicationDocumentsDirectory();
    final logFile = File('${dir.path}/video_export_debug.log');
    if (await logFile.exists()) {
      return await logFile.readAsString();
    } else {
      return 'No log file found';
    }
  } catch (e) {
    return 'Error reading log: $e';
  }
}

// Method to get logs for debugging (can be called from UI)
Future<String> getDebugLogs() async {
  return await _readLogs();
}

// Helper method to merge audio tracks with the combined video
Future<String?> _mergeAudioTracksWithVideo(String videoPath,
    {bool muteOriginal = false}) async {
  final tempDir = await getTemporaryDirectory();
  final targetSize = _getAspectRatioDimensions();
  final targetWidth = targetSize.width.toInt();
  final targetHeight = targetSize.height.toInt();

  await _writeLog('=== Starting _mergeAudioTracksWithVideo ===');
  await _writeLog('Input video path: $videoPath');
  await _writeLog('Target dimensions: ${targetWidth}x${targetHeight}');
  await _writeLog('Number of audio tracks to merge: ${_audioTracks.length}');

  print('=== Starting _mergeAudioTracksWithVideo ===');
  print('Input video path: $videoPath');
  print('Target dimensions: ${targetWidth}x${targetHeight}');
  print('Number of audio tracks to merge: ${_audioTracks.length}');

  // Ensure the input video has an audio stream (even if silent)
  final videoWithAudio = await ensureAudio(File(videoPath));
  String inputFiles = '-i "${videoWithAudio.path}" ';
  String filterComplex = '';
  List<String> audioInputs = [];

  // Add all audio files as inputs
  for (int i = 0; i < _audioTracks.length; i++) {
    var track = _audioTracks[i];
    inputFiles += '-i "${track.audioFile.path}" ';
    await _writeLog('Added audio input $i: ${track.audioFile.path}');
    print('Added audio input $i: ${track.audioFile.path}');
  }

  // Add video filter for aspect ratio
  filterComplex +=
      '[0:v]scale=$targetWidth:$targetHeight:force_original_aspect_ratio=decrease,pad=$targetWidth:$targetHeight:(ow-iw)/2:(oh-ih)/2:color=black[v]; ';

  // Check if original video has audio (not just silent audio stream)
  final hasAudio = await _hasAudioStream(videoWithAudio.path);
  await _writeLog('Original video has audio: $hasAudio');
  print('Original video has audio: $hasAudio');

  // Handle original video audio only if it exists and not muted
  if (hasAudio && !muteOriginal) {
    filterComplex += '[0:a]volume=1[orig]; ';
    audioInputs.add('[orig]');
  }

  // Handle additional audio tracks with delays, duration trimming, and mute
  for (int i = 0; i < _audioTracks.length; i++) {
    var track = _audioTracks[i];
    int delayMs = (track.trimStartTime * 1000).toInt();
    final isMuted = audioMuteStates[track.id] ?? false;
    final volume = isMuted ? 0 : 1.0;

    // Calculate the duration to trim the audio track
    final audioDuration = track.totalDuration;

    await _writeLog(
        'Audio track $i: delay=${delayMs}ms, duration=${audioDuration}s, volume=$volume');
    print(
        'Audio track $i: delay=${delayMs}ms, duration=${audioDuration}s, volume=$volume');

    if (delayMs > 0) {
      // Apply delay, trim duration, and volume
      filterComplex +=
          '[${i + 1}:a]atrim=duration=$audioDuration,adelay=${delayMs}|${delayMs},volume=$volume[a${i + 1}]; ';
    } else {
      // Apply duration trim and volume only
      filterComplex +=
          '[${i + 1}:a]atrim=duration=$audioDuration,volume=$volume[a${i + 1}]; ';
    }
    audioInputs.add('[a${i + 1}]');
  }

  String outputPath =
      '${tempDir.path}/export_${DateTime.now().millisecondsSinceEpoch}.mp4';

  String command;
  if (audioInputs.length > 1) {
    // Mix all audio inputs - use duration=first to match video duration
    filterComplex +=
        '${audioInputs.join('')}amix=inputs=${audioInputs.length}:duration=first:normalize=1[mixout]';
    command =
        '$inputFiles -filter_complex "$filterComplex" -map "[v]" -map "[mixout]" -c:v h264 -preset medium -crf 23 -r 30 -c:a aac -b:a 256k -ar 48000 "$outputPath"';
  } else if (audioInputs.length == 1) {
    // Only one audio source (original or one additional)
    String audioMap = audioInputs.first;
    // Map the single audio stream directly
    command =
        '$inputFiles -filter_complex "$filterComplex" -map "[v]" -map "$audioMap" -c:v h264 -preset medium -crf 23 -r 30 -c:a aac -b:a 256k -ar 48000 "$outputPath"';
  } else {
    // No audio
    command =
        '$inputFiles -filter_complex "$filterComplex" -map "[v]" -an -c:v h264 -preset medium -crf 23 -r 30 "$outputPath"';
  }

  await _writeLog('FFmpeg command (audio mixing): $command');
  await _writeLog('Filter complex: $filterComplex');
  print('FFmpeg command (audio mixing): $command');
  print('Filter complex: $filterComplex');

  final session = await FFmpegKit.execute(command);
  final returnCode = await session.getReturnCode();
  final ffmpegOutput = await session.getOutput();
  print('FFmpeg output:');
  print(ffmpegOutput);
  await _writeLog('FFmpeg output: $ffmpegOutput');
  if (ReturnCode.isSuccess(returnCode)) {
    await _writeLog('Successfully created mixed audio output: $outputPath');
    print('Successfully created mixed audio output: $outputPath');
    return outputPath;
  } else {
    final logs = await session.getOutput();
    await _writeLog('FFmpeg error: $logs');
    print('FFmpeg error: $logs');
    return null;
  }
}

/// Directly export the combined video file after combineMediaFiles, skipping all post-processing
Future<String?> exportCombinedVideoDirectly(String outputPath) async {
  // Get target dimensions
  final targetSize = _getAspectRatioDimensions();
  final processedFiles = _videoTracks.map((t) => t.processedFile).toList();
  final (
    File? combined,
    List<(File, File)>? processedPairs,
  ) = await EditorVideoController.combineMediaFiles(
    processedFiles,
    outputHeight: targetSize.height.toInt(),
    outputWidth: targetSize.width.toInt(),
  );
  if (combined == null) {
    print('Failed to combine video segments for direct export');
    return null;
  }
  print(
      'Direct export: combined file path: \\${combined.path}, size: \\${await combined.length()} bytes');
  final outputFile = await File(combined.path).copy(outputPath);
  print(
      'Direct export: output file path: \\${outputFile.path}, size: \\${await outputFile.length()} bytes');
  return outputFile.path;
}

/// Process individual video segments for mute state before combining
Future<List<File>> processVideoSegmentsForMute() async {
  final tempDir = await getTemporaryDirectory();
  final List<File> processedSegments = [];

  print('=== Processing video segments for mute state ===');
  print('Total segments: ${_videoTracks.length}');

  for (int i = 0; i < _videoTracks.length; i++) {
    final track = _videoTracks[i];
    final isMuted = isVideoMuted(track.id);

    print(
        'Segment $i: id=${track.id}, muted=$isMuted, path=${track.processedFile.path}');

    if (isMuted) {
      // Step 1: Set original audio volume to 0
      final tempMutedPath =
          '${tempDir.path}/temp_muted_segment_${i}_${DateTime.now().millisecondsSinceEpoch}.mp4';
      final volumeMuteCmd =
          '-y -i "${track.processedFile.path}" -af "volume=0" -c:v copy -c:a aac "$tempMutedPath"';
      print('Muting original audio for segment $i: $volumeMuteCmd');
      final muteSession = await FFmpegKit.execute(volumeMuteCmd);
      final muteReturnCode = await muteSession.getReturnCode();
      if (!ReturnCode.isSuccess(muteReturnCode)) {
        print('Failed to mute original audio for segment $i, using original');
        processedSegments.add(track.processedFile);
        continue;
      }

      // Step 2: Add silent audio using anullsrc
      final mutedPath =
          '${tempDir.path}/muted_segment_${i}_${DateTime.now().millisecondsSinceEpoch}.mp4';
      final anullsrcCmd =
          '-y -i "$tempMutedPath" -f lavfi -i anullsrc=channel_layout=stereo:sample_rate=48000 -shortest -c:v copy -c:a aac "$mutedPath"';
      print('Adding silent audio for segment $i: $anullsrcCmd');
      final anullsrcSession = await FFmpegKit.execute(anullsrcCmd);
      final anullsrcReturnCode = await anullsrcSession.getReturnCode();
      if (ReturnCode.isSuccess(anullsrcReturnCode)) {
        processedSegments.add(File(mutedPath));
        print('Muted segment $i processed successfully: $mutedPath');
        // Verify the muted segment has silent audio
        final verifySession =
            await FFmpegKit.execute('-i "$mutedPath" -hide_banner');
        final verifyOutput = await verifySession.getOutput() ?? '';
        final verifyLogs = await verifySession.getLogsAsString() ?? '';
        final allVerifyOutput = verifyOutput + verifyLogs;
        print(
            'Muted segment $i audio check: ${allVerifyOutput.contains('Audio:') ? 'Has audio stream' : 'No audio stream'}');
        if (allVerifyOutput.contains('Audio:')) {
          print(
              'Muted segment $i audio details: ${allVerifyOutput.split('Audio:').last.split('\n').first}');
        }
      } else {
        print('Failed to add silent audio for segment $i, using original');
        processedSegments.add(track.processedFile);
      }
    } else {
      // Keep original audio for unmuted segments
      processedSegments.add(track.processedFile);
      print('Unmuted segment $i: keeping original audio');
    }
  }

  print('=== Finished processing video segments ===');
  return processedSegments;
}

extension ListStack<T> on List<T> {
  void push(T item) {
    add(item);
  }

  T pop() {
    if (isEmpty) {
      throw StateError('Cannot pop from an empty stack');
    }
    return removeLast();
  }

  void clearStack() => clear();
}

class EditOperation {
  final EditOperationType type;
  final dynamic oldState;
  final dynamic newState;

  EditOperation(this.type, this.oldState, this.newState);

  EditOperation reverse() => EditOperation(type, newState, oldState);
}

enum EditOperationType {
  text,
  filter,
  trim,
  crop,
  rotation,
  transition,
  speed,
  asset,
  caption,
  stretch,
}

enum EditMode {
  none, // Default mode, no editing operation active
  trim, // Trimming video duration
  crop, // Cropping video frame
  text, // Adding/editing text overlays
  filter, // Applying visual filters
  transition, // Adding/editing transitions
  audio, // Editing audio/sound
  speed, // Adjusting playback speed
  rotate, // Rotating video
  asset, // Managing assets (images/videos)
  volume, // Adjusting volume levels
  caption, // Adding/editing captions
}

}
