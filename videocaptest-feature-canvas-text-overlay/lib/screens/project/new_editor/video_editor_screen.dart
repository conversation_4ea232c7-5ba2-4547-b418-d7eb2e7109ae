import 'package:flutter/material.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/video_editor_provider.dart';
import 'package:ai_video_creator_editor/widgets/overlay_video_track_list.dart';
import 'package:ai_video_creator_editor/widgets/overlay_video_trimmer.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/video_preview.dart';
import 'package:provider/provider.dart';
import 'package:video_player/video_player.dart';

class VideoEditorScreen extends StatefulWidget {
  @override
  State<VideoEditorScreen> createState() => _VideoEditorScreenState();
}

class _VideoEditorScreenState extends State<VideoEditorScreen> {
  List<VideoPlayerController> _overlayControllers = [];
  VideoPlayerController? _mainController;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final provider = Provider.of<VideoEditorProvider>(context);
    _initMainController(provider);
    _initOverlayControllers(provider);
  }

  void _initMainController(VideoEditorProvider provider) {
    final videoController = provider.videoEditorController?.video;
    if (videoController != null && videoController != _mainController) {
      _mainController?.dispose();
      _mainController = videoController;
    }
  }

  void _initOverlayControllers(VideoEditorProvider provider) {
    for (final c in _overlayControllers) {
      c.dispose();
    }
    _overlayControllers = provider.overlayVideoTracks.map((track) {
      final ctrl = VideoPlayerController.file(track.videoFile);
      ctrl.initialize();
      return ctrl;
    }).toList();
    _syncOverlayControllers();
  }

  void _syncOverlayControllers() {
    if (_mainController == null) return;
    for (final ctrl in _overlayControllers) {
      ctrl.setVolume(0);
      if (_mainController!.value.isPlaying) {
        ctrl.play();
      } else {
        ctrl.pause();
      }
      ctrl.seekTo(_mainController!.value.position);
    }
    _mainController!.addListener(_onMainVideoPositionChanged);
  }

  void _onMainVideoPositionChanged() {
    if (_mainController == null) return;
    for (final ctrl in _overlayControllers) {
      if ((ctrl.value.position - _mainController!.value.position)
              .inMilliseconds
              .abs() >
          100) {
        ctrl.seekTo(_mainController!.value.position);
      }
      if (_mainController!.value.isPlaying && !ctrl.value.isPlaying) {
        ctrl.play();
      } else if (!_mainController!.value.isPlaying && ctrl.value.isPlaying) {
        ctrl.pause();
      }
    }
  }

  @override
  void dispose() {
    for (final c in _overlayControllers) {
      c.dispose();
    }
    _mainController?.removeListener(_onMainVideoPositionChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final provider = Provider.of<VideoEditorProvider>(context);
    return Scaffold(
      appBar: AppBar(
        title: const Text('Video Editor'),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: () async {
              final outputPath = await provider.mergeMultipleVideosToVideo(
                context,
                inputPath: provider.currentVideoPath!,
                muteOriginal: false,
              );
              if (outputPath != null) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Exported to $outputPath')),
                );
              } else {
                ScaffoldMessenger.of(
                  context,
                ).showSnackBar(const SnackBar(content: Text('Export failed')));
              }
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            if (_mainController != null && _mainController!.value.isInitialized)
              VideoPreview(
                controller: _mainController!,
                textOverlays: provider.textOverlays,
                overlayTracks: provider.overlayVideoTracks,
                overlayControllers: _overlayControllers,
              ),
            ElevatedButton(
              onPressed: () => provider.pickOverlayVideoFile(context),
              child: const Text('Add Overlay Video'),
            ),
            const SizedBox(height: 8),
            OverlayVideoTrackList(
              tracks: provider.overlayVideoTracks,
              onRemove: (index) => provider.removeOverlayVideoTrack(index),
              onEdit: (index) async {
                final track = provider.overlayVideoTracks[index];
                final result = await Navigator.push<Map<String, dynamic>>(
                  context,
                  MaterialPageRoute(
                    builder: (context) => OverlayVideoTrimmer(
                      videoFile: track.videoFile,
                      videoDuration: track.totalDuration,
                      remainVideoDuration:
                          provider.videoDuration - track.trimStartTime,
                    ),
                  ),
                );
                if (result != null) {
                  provider.updateOverlayVideoTrack(
                    index,
                    track.trimStartTime,
                    track.trimStartTime + (result['totalDuration'] as double),
                    opacity: result['opacity'] as double?,
                    blendMode: result['blendMode'] as String?,
                    position: result['position'] as Rect?,
                  );
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}
