import 'package:flutter/material.dart';
import '../screens/project/models/overlay_video_track_model.dart';

class OverlayVideoTrackList extends StatelessWidget {
  final List<OverlayVideoTrackModel> tracks;
  final void Function(int) onRemove;
  final void Function(int) onEdit;

  const OverlayVideoTrackList({
    required this.tracks,
    required this.onRemove,
    required this.onEdit,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (tracks.isEmpty) {
      return const Text('No overlay videos added.');
    }
    return ListView.builder(
      shrinkWrap: true,
      itemCount: tracks.length,
      itemBuilder: (context, index) {
        final track = tracks[index];
        return ListTile(
          title: Text(
            'Overlay  ${index + 1} (${track.videoFile.path.split('/').last})',
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          subtitle: Text(
            'Start: ${track.trimStartTime}s, End: ${track.trimEndTime}s, Opacity: ${track.opacity}',
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                icon: const Icon(Icons.edit),
                onPressed: () => onEdit(index),
              ),
              IconButton(
                icon: const Icon(Icons.delete),
                onPressed: () => onRemove(index),
              ),
            ],
          ),
        );
      },
    );
  }
}
