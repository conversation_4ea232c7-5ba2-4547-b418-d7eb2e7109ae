// GENERATED CODE - DO NOT MODIFY BY HAND
// This code was generated by ObjectBox. To update it run the generator again
// with `dart run build_runner build`.
// See also https://docs.objectbox.io/getting-started#generate-objectbox-code

// ignore_for_file: camel_case_types, depend_on_referenced_packages
// coverage:ignore-file

import 'dart:typed_data';

import 'package:flat_buffers/flat_buffers.dart' as fb;
import 'package:objectbox/internal.dart'
    as obx_int; // generated code can access "internal" functionality
import 'package:objectbox/objectbox.dart' as obx;
import 'package:objectbox_flutter_libs/objectbox_flutter_libs.dart';

import 'database/models/generated_audio_meta.dart';

export 'package:objectbox/objectbox.dart'; // so that callers only have to import this file

final _entities = <obx_int.ModelEntity>[
  obx_int.ModelEntity(
      id: const obx_int.IdUid(1, 3885931900534919121),
      name: 'GeneratedAudioMeta',
      lastPropertyId: const obx_int.IdUid(4, 865273178067906958),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 7536604391649525395),
            name: 'id',
            type: 6,
            flags: 1),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 4610291104066293756),
            name: 'prompt',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 372239420740692067),
            name: 'originalFilePath',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(4, 865273178067906958),
            name: 'trimmedFilePath',
            type: 9,
            flags: 0)
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[])
];

/// Shortcut for [obx.Store.new] that passes [getObjectBoxModel] and for Flutter
/// apps by default a [directory] using `defaultStoreDirectory()` from the
/// ObjectBox Flutter library.
///
/// Note: for desktop apps it is recommended to specify a unique [directory].
///
/// See [obx.Store.new] for an explanation of all parameters.
///
/// For Flutter apps, also calls `loadObjectBoxLibraryAndroidCompat()` from
/// the ObjectBox Flutter library to fix loading the native ObjectBox library
/// on Android 6 and older.
Future<obx.Store> openStore(
    {String? directory,
    int? maxDBSizeInKB,
    int? maxDataSizeInKB,
    int? fileMode,
    int? maxReaders,
    bool queriesCaseSensitiveDefault = true,
    String? macosApplicationGroup}) async {
  await loadObjectBoxLibraryAndroidCompat();
  return obx.Store(getObjectBoxModel(),
      directory: directory ?? (await defaultStoreDirectory()).path,
      maxDBSizeInKB: maxDBSizeInKB,
      maxDataSizeInKB: maxDataSizeInKB,
      fileMode: fileMode,
      maxReaders: maxReaders,
      queriesCaseSensitiveDefault: queriesCaseSensitiveDefault,
      macosApplicationGroup: macosApplicationGroup);
}

/// Returns the ObjectBox model definition for this project for use with
/// [obx.Store.new].
obx_int.ModelDefinition getObjectBoxModel() {
  final model = obx_int.ModelInfo(
      entities: _entities,
      lastEntityId: const obx_int.IdUid(1, 3885931900534919121),
      lastIndexId: const obx_int.IdUid(0, 0),
      lastRelationId: const obx_int.IdUid(0, 0),
      lastSequenceId: const obx_int.IdUid(0, 0),
      retiredEntityUids: const [],
      retiredIndexUids: const [],
      retiredPropertyUids: const [],
      retiredRelationUids: const [],
      modelVersion: 5,
      modelVersionParserMinimum: 5,
      version: 1);

  final bindings = <Type, obx_int.EntityDefinition>{
    GeneratedAudioMeta: obx_int.EntityDefinition<GeneratedAudioMeta>(
        model: _entities[0],
        toOneRelations: (GeneratedAudioMeta object) => [],
        toManyRelations: (GeneratedAudioMeta object) => {},
        getId: (GeneratedAudioMeta object) => object.id,
        setId: (GeneratedAudioMeta object, int id) {
          object.id = id;
        },
        objectToFB: (GeneratedAudioMeta object, fb.Builder fbb) {
          final promptOffset =
              object.prompt == null ? null : fbb.writeString(object.prompt!);
          final originalFilePathOffset = object.originalFilePath == null
              ? null
              : fbb.writeString(object.originalFilePath!);
          final trimmedFilePathOffset = object.trimmedFilePath == null
              ? null
              : fbb.writeString(object.trimmedFilePath!);
          fbb.startTable(5);
          fbb.addInt64(0, object.id);
          fbb.addOffset(1, promptOffset);
          fbb.addOffset(2, originalFilePathOffset);
          fbb.addOffset(3, trimmedFilePathOffset);
          fbb.finish(fbb.endTable());
          return object.id;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final idParam =
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);
          final promptParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 6);
          final originalFilePathParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 8);
          final trimmedFilePathParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 10);
          final object = GeneratedAudioMeta(
              id: idParam,
              prompt: promptParam,
              originalFilePath: originalFilePathParam,
              trimmedFilePath: trimmedFilePathParam);

          return object;
        })
  };

  return obx_int.ModelDefinition(model, bindings);
}

/// [GeneratedAudioMeta] entity fields to define ObjectBox queries.
class GeneratedAudioMeta_ {
  /// See [GeneratedAudioMeta.id].
  static final id =
      obx.QueryIntegerProperty<GeneratedAudioMeta>(_entities[0].properties[0]);

  /// See [GeneratedAudioMeta.prompt].
  static final prompt =
      obx.QueryStringProperty<GeneratedAudioMeta>(_entities[0].properties[1]);

  /// See [GeneratedAudioMeta.originalFilePath].
  static final originalFilePath =
      obx.QueryStringProperty<GeneratedAudioMeta>(_entities[0].properties[2]);

  /// See [GeneratedAudioMeta.trimmedFilePath].
  static final trimmedFilePath =
      obx.QueryStringProperty<GeneratedAudioMeta>(_entities[0].properties[3]);
}
